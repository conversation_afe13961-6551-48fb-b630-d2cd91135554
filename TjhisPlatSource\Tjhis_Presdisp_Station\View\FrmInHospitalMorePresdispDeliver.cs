using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraReports.UI;
using NM_Service.NMService;
using PlatCommon.Common;
using PlatCommon.SysBase;
using Tjhis.Presdisp.Station.Comm;
using Tjhis.Presdisp.Station.Common;

namespace Tjhis.Presdisp.Station.View
{
    public partial class FrmInHospitalMorePresdispDeliver : PlatCommon.SysBase.ParentForm
    {
        #region 变量
        string chargetypes = "";
        DataRow masterdr;//处方基础信息
        string prescdateprint = "";//重打时调用，发药完成时复制
        string prescnoprint = "";//重打时调用，发药完成时复制
        string canshu = "";//PlatCommon.System.SystemParm.GetParaValue("ISPASS", SystemParm.AppName, "*", "*", "0", PlatCommon.System.SystemParm.HisUnitCode);
        DataTable dt_print = new DataTable();//打印数据集
        Dictionary<string, string> list_item;
        string DISPENSING_DATETIME_PRINT = "";//保存的时间
        #endregion

        #region 事件
        public FrmInHospitalMorePresdispDeliver()
        {
            InitializeComponent();
        }

        private void FrmInHospitalMorePresdispDeliver_Load(object sender, EventArgs e)
        {
            canshu = PlatCommon.SysBase.SystemParm.GetParameterValue("ISPASS", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);


            Getdt_printClone();
            SetDept();
            SetLupPrescAttr();
            SetPrescAttr();
            if (canshu == "1")
            {
                //barLargeButtonItem1.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barLargeButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

        }
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BbtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            list_item = new Dictionary<string, string>();

            gv1.CloseEditor();
            gv1.UpdateCurrentRow();
            DataTable batchnodt = GetBatchProvideNo();
            string batchno = "";
            string ls_success_presno = "", ls_failed_presno = "";//成功处方列表，失败处方列表
            if (batchnodt.Rows.Count > 0)
            {
                batchno = batchnodt.Rows[0][0].ToString();
            }
            else
            {
                batchno = GetSystemTime().ToString("yyyyMMdd") + "000001";
            }

            DataTable masterlist = (DataTable)gc1.DataSource;
            if (masterlist == null) return;
            DataRow[] selectmaster = masterlist.Select("AS_COMFIRM='1'");//获取处方主列表选中行
            if (selectmaster.Length == 0)
            {
                //XtraMessageBox.Show("请选择要批量发药的处方!", "提示");
                return;
            }
            //保存时是否弹出确认提示框
            string commitConfirm = PlatCommon.SysBase.SystemParm.GetParameterValue("COMMIT_CONFIRM", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (commitConfirm.Length > 0 && Convert.ToInt16(commitConfirm) > 0)
            {
                if (XtraMessageBox.Show("是否确认？", "提示", MessageBoxButtons.OKCancel) == DialogResult.Cancel)
                {
                    return;
                }
            }
            //保存时是否需要重新输入密码
            string confirmlogin = PlatCommon.SysBase.SystemParm.GetParameterValue("CONFIRM_LOGIN", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (confirmlogin == "1")
            {
                Comm.FrmPrescLogin presclogin = new Comm.FrmPrescLogin();
                presclogin.ShowDialog();
                if (!presclogin.resultstr) return;
            }
            //存批量发药的处方日期和编号，用于打印是一起打印
            DataTable printdt = new DataTable();
            printdt.Columns.Add("PRESC_DATE");
            printdt.Columns.Add("PRESC_NO");
            DISPENSING_DATETIME_PRINT = PlatCommon.Common.PublicFunction.GetSysDate();
            //批量保存
            for (int i = 0; i < selectmaster.Length; i++)
            {
                Dictionary<string, string> allsave = new Dictionary<string, string>();
                //masterdr=selectmaster[i];
                DataTable masterdt = GetDoctPrescMasterTempByDateAndNo(Convert.ToDateTime(selectmaster[i]["PRESC_DATE"]), selectmaster[i]["PRESC_NO"].ToString());
                if (masterdt.Rows.Count > 0)
                {
                    txtPatientID.Text = masterdt.Rows[0]["PATIENT_ID"].ToString();
                    txtName.Text = masterdt.Rows[0]["NAME"].ToString();
                    txtAge.Text = masterdt.Rows[0]["AGE"].ToString();
                    txtSex.Text = masterdt.Rows[0]["SEX"].ToString();
                    txtIdentity.Text = masterdt.Rows[0]["IDENTITY"].ToString();
                    txtChargeType.Text = masterdt.Rows[0]["CHARGE_TYPE"].ToString();
                    sluePrescAttr.EditValue = masterdt.Rows[0]["PRESC_ATTR"] != DBNull.Value ? masterdt.Rows[0]["PRESC_ATTR"].ToString() : "";//处方属性
                    txtPrescDate.Text = masterdt.Rows[0]["PRESC_DATE"].ToString();//开方日期
                    txtPrescNo.Text = masterdt.Rows[0]["PRESC_NO"].ToString();
                    txtPrescribed.Text = masterdt.Rows[0]["PRESCRIBED_BY"].ToString();//开单医生
                    txtDeptName.Text = masterdt.Rows[0]["ORDERED_BY"].ToString();//科室
                    //txtPrescType.Text = masterdt.Rows[0]["ORDERED_BY"].ToString() == "1" ? "中药" : "西药";//处方类别
                    txtYu.Text = masterdt.Rows[0]["PREPAYMENT"].ToString();//预交金
                    txtJi.Text = masterdt.Rows[0]["COSTS"].ToString();//计价
                    txtYing.Text = masterdt.Rows[0]["PAYMENTS"].ToString();//应收
                    txtAdmin.Text = masterdt.Rows[0]["USAGE"] != DBNull.Value ? masterdt.Rows[0]["USAGE"].ToString() : "";//用法

                    txtRepetition.Text = masterdt.Rows[0]["REPETITION"].ToString();//剂树
                    txtCountPerRepetition.Text = masterdt.Rows[0]["COUNT_PER_REPETITION"] != DBNull.Value ? masterdt.Rows[0]["COUNT_PER_REPETITION"].ToString() : "";//每剂/份
                    // 根据DECOCTION字段值显示对应的代煎状态
                    string decoctionValue = masterdt.Rows[0]["DECOCTION"].ToString();
                    switch (decoctionValue)
                    {
                        case "0":
                            txtDecoction.Text = "不代煎";
                            break;
                        case "1":
                            txtDecoction.Text = "人工代煎";
                            break;
                        case "2":
                            txtDecoction.Text = "机器代煎";
                            break;
                        default:
                            txtDecoction.Text = "不代煎";
                            break;
                    }
                    if (masterdt.Rows[0]["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value)
                    {
                        txtDischargeTaking.Text = masterdt.Rows[0]["DISCHARGE_TAKING_INDICATOR"].ToString() == "1" ? "出院带药" : "不带药";
                    }
                    else
                    {
                        txtDischargeTaking.Text = "";
                    }

                    masterdr = masterdt.Rows[0];
                }
                else
                {
                    continue;
                }

                int right = SaveOnePresc(batchno, ref allsave);
                if (right < 0)
                {
                    ls_failed_presno = masterdr["PRESC_NO"].ToString() + ",";
                    continue;
                }

                if (allsave.Count > 0)
                {
                    string result = new ServerPublicClient().SaveTable(allsave);
                    if (result.Length > 0)
                    {
                        ls_failed_presno += masterdr["PRESC_NO"].ToString() + ",";
                        XtraMessageBox.Show(result + "处方号：" + masterdr["PRESC_NO"].ToString(), "保存失败");
                        return;
                    }
                    else
                    {
                        ls_success_presno += masterdr["PRESC_NO"].ToString() + ",";
                        DataRow printdr = printdt.NewRow();
                        printdr["PRESC_DATE"] = masterdr["PRESC_DATE"];
                        printdr["PRESC_NO"] = masterdr["PRESC_NO"];
                        prescdateprint = masterdr["PRESC_DATE"].ToString();
                        prescnoprint = masterdr["PRESC_NO"].ToString();
                        printdt.Rows.Add(printdr);

                    }
                }
            }
            string message = "处方发药完毕!" + "\n";
            message += "成功处方：" + ls_success_presno + "\n";
            message += "失败处方：" + ls_failed_presno + "\n";
            XtraMessageBox.Show(message, "提示");
            string prnflag = PlatCommon.SysBase.SystemParm.GetParameterValue("PRNFLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            //int ipc = 0;
            if (printdt.Rows.Count > 0)
            {
                if (prnflag == "1")
                {
                    Hashtable hasParam = new Hashtable
                    {
                        { "BATCH_PROVIDE_NO", batchno  }
                    };
                    DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("住院处方批量发药", hasParam, this.AppCode);
                    XtraReportHelper.Print("住院处方批量发药", dsPrint, this.AppCode);
                }
                else if (prnflag == "2")
                {
                    if (XtraMessageBox.Show("是否打印?", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                    {
                        Hashtable hasParam = new Hashtable
                    {
                        { "BATCH_PROVIDE_NO", batchno  }
                    };

                        DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("住院处方批量发药", hasParam, this.AppCode);
                        XtraReportHelper.Print("住院处方批量发药", dsPrint, this.AppCode);
                    }
                }

                #region 统一接口 2022-04-14
                string strSQL = " SELECT * FROM DRUG_PRESC_MASTER " +
                    " WHERE BATCH_PROVIDE_NO = '" + batchno + "'";
                DataTable dataTableTEMP = new ServerPublicClient().GetDataBySql(strSQL).Tables[0];
                DataTable[] dataTables = new DataTable[] { dataTableTEMP };
                string strERROR_TEXT = string.Empty;
                Tjhis.Interface.Station.Interface_Common.InvokeInterface("Presdisp_004", "UPDATE", this.AppName, this.DeptCode, dataTables, ref strERROR_TEXT);
                #endregion

                bbtnrefresh_ItemClick(null, null);
            }
        }
        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnrefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //不重置科室和处方类型，按当前条件更新 by lions 2019-05-24
            //slueDept.Text = "";
            //cmbPrescType.SelectedIndex = -1;
            CleanTxt();
            CleanStorgeTxt();
            gc2.DataSource = null;
            barEditItem1.EditValue = true;
            SetPrescAttr();
            SetPrescMaster();

        }
        /// <summary>
        /// 退出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnExit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        /// <summary>
        /// 单击病人，查询处方主信息和明细
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            SetBaseInfoAndDetailByPrescDateAndPrescNo(e.RowHandle);
        }
        /// <summary>
        /// 点击明细，查询库存和单价
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv2_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DataTable drugdt = (DataTable)gc2.DataSource;
            if (drugdt.Rows.Count > 0)
            {
                //显示零售价和库存
                lbStockCount.Text = GetDrugStockByDrugCode(this.DeptCode, drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString(), drugdt.Rows[e.RowHandle]["PACKAGE_SPEC"].ToString()).Rows[0][0].ToString();
                DataTable drugprice = GetDrugPriceDrugCode(drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["DRUG_SPEC"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
            }
            else
            {
                lbStockCount.Text = "";
                lbPrice.Text = "";
            }
        }

        string dept = "";
        /// <summary>
        /// 科室类型变化，筛选
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void slueDept_EditValueChanged(object sender, EventArgs e)
        {
            if (this.slueDept.EditValue == null) return;
            string sql = "select dept_code from dept_vs_ward where ward_code ='" + this.slueDept.EditValue + "' ";

            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            dept = dt.Rows[0]["dept_code"].ToString();
            //string deptcode = slueDept.EditValue == null ? "" : slueDept.EditValue.ToString();
            //lup_SpescAttr.Properties.DataSource = GetBedGroup(deptcode);
            //lup_SpescAttr.Properties.ValueMember = "bed_group".ToUpper();
            //lup_SpescAttr.Properties.DisplayMember = "bed_group".ToUpper();
            SetPrescMaster();
        }
        /// <summary>
        /// 处方类型变化，筛选
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cmbPrescType_SelectedIndexChanged(object sender, EventArgs e)
        {
            //  SetPrescMaster();
        }
        /// <summary>
        /// 预交金负数变色
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtYu_TextChanged(object sender, EventArgs e)
        {
            if (txtYu.Text == "") return;
            else if (Convert.ToDecimal(txtYu.Text) < 0)
            {
                txtYu.ForeColor = Color.Red;
            }
            else
            {
                txtYu.ForeColor = Color.Black;
            }
        }
        #endregion

        #region 方法

        /// <summary>
        /// 重打
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <param name="isfirst"></param>
        private void RepertPrint(string prescdate, string prescno, bool isfirst)
        {
            int prisctype = 0;
            string patientid = "";
            string prescribedby = "";//开单医生姓名
            DataTable prescmasterdt = GetDrugPrescMasterByPrescDateAndNo(prescdate, prescno);
            DataTable printdt;
            if (prescmasterdt.Rows.Count > 0)
            {
                prisctype = Convert.ToInt16(prescmasterdt.Rows[0]["presc_type"]);
                patientid = prescmasterdt.Rows[0]["patient_id"].ToString();
                prescribedby = prescmasterdt.Rows[0]["prescribed_by"].ToString();
            }
            else return;
            int printindex = 0;
            string printtype = PlatCommon.SysBase.SystemParm.GetParameterValue("PRINTERTYPE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (printtype == "normal")
            {
                if (prisctype == 0)
                {
                    //d_pip_print_doct_presc_master_normal_west
                    printdt = GetDrugPrescByPrint(prescdate, prescno);
                    printindex = 1;
                }
                else
                {
                    //d_pip_print_doct_presc_master_normal_china1
                    printdt = GetDrugPrescByPrint1(prescdate, prescno);
                    printindex = 2;
                }
            }
            else
            {
                //d_pip_print_doct_presc_master_normal_hot
                printdt = GetDrugPrescByPrint(prescdate, prescno);
                printindex = 3;
            }
            if (prisctype == 0)
            {
                //d_pip_print_doct_presc_master_normal_west
                printdt = GetDrugPrescByPrint(prescdate, prescno);
                printindex = 1;
            }
            else
            {
                //d_pip_print_doct_presc_master_normal_china1
                printdt = GetDrugPrescByPrint1(prescdate, prescno);
                printindex = 2;
            }
            printdt = GetDrugPrescByPrint(prescdate, prescno);
            if (printdt.Rows.Count < 1)
            {
                printdt = GetDrugPrescByPrint1(prescdate, prescno);
                printindex = 4;
            }
            if (printdt.Rows.Count == 0) return;
            //dt_print = printdt.Clone();
            foreach (DataRow dr in printdt.Rows)
            {
                DataRow dr_new = dt_print.NewRow();
                dr_new["PRESC_DATE"] = dr["PRESC_DATE"];
                dr_new["PRESC_NO"] = dr["PRESC_NO"];
                dr_new["AGE"] = dr["AGE"];
                dr_new["SEX"] = dr["SEX"];
                dr_new["DISPENSARY"] = dr["DISPENSARY"];
                dr_new["DISPENSARY_NAME"] = dr["DISPENSARY_NAME"];
                dr_new["PATIENT_ID"] = dr["PATIENT_ID"];
                dr_new["NAME"] = dr["NAME"];
                dr_new["BED_LABEL"] = dr["BED_LABEL"];
                dr_new["IDENTITY"] = dr["IDENTITY"];
                dr_new["CHARGE_TYPE"] = dr["CHARGE_TYPE"];
                dr_new["UNIT_IN_CONTRACT"] = dr["UNIT_IN_CONTRACT"];
                dr_new["PRESC_SOURCE"] = dr["PRESC_SOURCE"];
                dr_new["REPETITION"] = dr["REPETITION"];
                dr_new["ORDERED_BY"] = dr["ORDERED_BY"];
                dr_new["PRESCRIBED_BY"] = dr["PRESCRIBED_BY"];
                dr_new["VISIT_ID"] = dr["VISIT_ID"];
                dr_new["ITEM_NO"] = dr["ITEM_NO"];
                dr_new["DRUG_NAME"] = dr["DRUG_NAME"];
                dr_new["FIRM_ID"] = dr["FIRM_ID"];
                dr_new["PACKAGE_SPEC"] = dr["PACKAGE_SPEC"];
                dr_new["PACKAGE_UNITS"] = dr["PACKAGE_UNITS"];
                dr_new["QUANTITY"] = dr["QUANTITY"];
                dr_new["NAME_PHONETIC"] = dr["NAME_PHONETIC"];
                dr_new["PRESC_TYPE"] = dr["PRESC_TYPE"];
                dr_new["COSTS"] = dr["COSTS"];
                dr_new["PAYMENTS"] = dr["PAYMENTS"];
                dr_new["ENTERED_BY"] = dr["ENTERED_BY"];
                dr_new["COUNT_PER_REPETITION"] = dr["COUNT_PER_REPETITION"];
                dr_new["DCOSTS"] = dr["DCOSTS"];
                dr_new["FREQUENCY"] = dr["FREQUENCY"];
                dr_new["DOSAGE_EACH"] = dr["DOSAGE_EACH"];
                dr_new["DOSAGE"] = dr["DOSAGE"];
                dr_new["DOSAGE_UNITS"] = dr["DOSAGE_UNITS"];
                dr_new["ADMINISTRATION"] = dr["ADMINISTRATION"];
                dr_new["USAGE"] = dr["USAGE"];
                dr_new["DISCHARGE_TAKING_INDICATOR"] = dr["DISCHARGE_TAKING_INDICATOR"];
                dr_new["DACOSTS"] = dr["DACOSTS"];
                dr_new["DEPT_NAME"] = dr["DEPT_NAME"];
                dt_print.Rows.Add(dr_new);
            }
        }
        /// <summary>
        /// 保存一条处方记录
        /// </summary>
        /// <param name="allsave"></param>
        private int SaveOnePresc(string batchno, ref Dictionary<string, string> allsave)
        {
            DataTable detaildt = GetDoctPrescDetailTempByDateAndNo(Convert.ToDateTime(masterdr["PRESC_DATE"]), masterdr["PRESC_NO"].ToString());
            Dictionary<string, string> tempsave = new Dictionary<string, string>();
            if (detaildt.Rows.Count > 0)
            {
                string chargetype = masterdr != null ? masterdr["CHARGE_TYPE"].ToString() : "";
                string presctype = masterdr != null ? masterdr["PRESC_TYPE"].ToString() : "0";
                string itemclass = presctype == "0" ? "A" : "B";
                decimal retailprice = 0;
                for (int i = 0; i < detaildt.Rows.Count; i++)
                {
                    if (detaildt.Rows[i]["FIRM_ID"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！";
                        return 0;
                    }
                    if (detaildt.Rows[i]["DRUG_SPEC"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！";
                        return 0;
                    }
                    if (detaildt.Rows[i]["package_spec"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！";
                        return 0;
                    }
                    DataTable pricedt = GetPriceListByClassCodeSpec(itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"]);
                    if (pricedt.Rows.Count > 0)
                    {
                        retailprice = Convert.ToDecimal(pricedt.Rows[0][0]);
                    }
                    //计算价格
                    DataTable coeffnumerator = GetChargePriceSchedule(chargetype, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    decimal decDefault = 1;
                    if (coeffnumerator.Rows.Count > 0)
                    {
                        decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
                    }
                    decimal decChargePrice = 0.00m;

                    PlatCommon.Common.PublicFunction.GetCalcChargePrice(chargetype, itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"], retailprice, ref decDefault, ref decChargePrice);
                    detaildt.Rows[i]["RETAIL_PRICE"] = retailprice;
                    detaildt.Rows[i]["COSTS"] = retailprice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);
                    detaildt.Rows[i]["PAYMENTS"] = decChargePrice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);

                }
            }
            //检索数据正确性
            DataTable prescdetadt = detaildt.Copy();
            for (int j = 0; j < prescdetadt.Rows.Count; j++)
            {
                if (prescdetadt.Rows[j]["ERR_MSG"] != DBNull.Value && prescdetadt.Rows[j]["ERR_MSG"].ToString().Length > 0)
                {
                    XtraMessageBox.Show(prescdetadt.Rows[j]["ERR_MSG"].ToString(), "提示");
                    return -1;
                }

                //增加整条处方库存量判断 如果其中有不足的就整个处方不给发药
                DataTable stockdt1 = GetDrugStockForUpdate(this.DeptCode, prescdetadt.Rows[j]["DRUG_CODE"].ToString(), prescdetadt.Rows[j]["DRUG_SPEC"].ToString(), prescdetadt.Rows[j]["PACKAGE_SPEC"].ToString(), prescdetadt.Rows[j]["FIRM_ID"].ToString());
                if (stockdt1.Rows.Count == 0)
                {
                    XtraMessageBox.Show("药品" + prescdetadt.Rows[j]["DRUG_NAME"] + "的库存不够，处方号'" + masterdr["PRESC_NO"].ToString() + "'不能发药！", "提示");
                    return 0;
                }
                decimal detailcount1 = Convert.ToDecimal(prescdetadt.Rows[j]["QUANTITY"]);
                decimal stockcount1 = Convert.ToDecimal(stockdt1.Compute("sum(QUANTITY)", "true"));//多条库存总数量
                if (stockcount1 < detailcount1)
                {
                    XtraMessageBox.Show("药品" + prescdetadt.Rows[j]["DRUG_NAME"] + "的库存数量不够,处方号'" + masterdr["PRESC_NO"].ToString() + "'不能发药！", "提示");
                    return 0;
                }

                // 【新增】统一库存验证 - 与门诊医生站保持一致的库存计算逻辑
                string drugCode = prescdetadt.Rows[j]["DRUG_CODE"].ToString();
                string drugName = prescdetadt.Rows[j]["DRUG_NAME"].ToString();
                string drugSpec = prescdetadt.Rows[j]["DRUG_SPEC"].ToString();
                string packageSpec = prescdetadt.Rows[j]["PACKAGE_SPEC"].ToString();
                string firmId = prescdetadt.Rows[j]["FIRM_ID"].ToString();
                string units = prescdetadt.Rows[j]["PACKAGE_UNITS"].ToString();

                // 2025-01-17 修复：使用处方中的药房代码，而不是当前科室代码
                string dispensary = masterdr != null && masterdr["DISPENSARY"] != DBNull.Value
                    ? masterdr["DISPENSARY"].ToString()
                    : this.DeptCode; // 如果处方中没有药房代码，则使用当前科室代码作为备选

                StockValidationResult validationResult = UnifiedStockValidator.ValidateStock(
                    drugCode, drugName, packageSpec, firmId, dispensary, detailcount1, units);

                if (!validationResult.IsValid)
                {
                    XtraMessageBox.Show($"【统一库存验证】{validationResult.ErrorMessage}\n处方号：{masterdr["PRESC_NO"]}", "库存验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return 0;
                }

            }

            string patientid = masterdr["PATIENT_ID"].ToString();
            int visitid = Convert.ToInt16(masterdr["VISIT_ID"].ToString());

            DataTable settledt = GetInpSettleMasterByPatientID(patientid, visitid);
            if (Convert.ToInt16(settledt.Rows[0][0]) > 0)
            {
                XtraMessageBox.Show("病人已经终结！", "提示");
                return -1;
            }
            //判断病人是否出院或者是否办理结帐
            DataTable inhospdt = GetPatInHospitapByPatientID(patientid);
            if (inhospdt.Rows.Count < 1)
            {
                XtraMessageBox.Show("病人已经出院！", "提示");
                return -1;
            }
            else
            {
                if (inhospdt.Rows[0]["SETTLED_INDICATOR"] != DBNull.Value && inhospdt.Rows[0]["SETTLED_INDICATOR"].ToString() == "1")
                {
                    XtraMessageBox.Show("该病人已经办理结帐!", "提示");
                    return -1;
                }
            }
            //判断新生儿，预交金
            DataTable newborn = CheckNewBorn(patientid);
            if (newborn.Rows.Count > 0)//是新生儿,下面所有的信息都取母亲的信息
            {
                patientid = newborn.Rows[0]["PATIENT_ID_OF_MOTHER"].ToString();
                visitid = Convert.ToInt16(newborn.Rows[0]["VISIT_ID_OF_MOTHER"]);
            }
            //判断预交金是否充足
            DataTable predt = GetPrepaymentByPatientID(patientid, visitid);
            predt.Rows[0][0] = predt.Rows[0][0] == DBNull.Value ? 0 : predt.Rows[0][0];
            if (Convert.ToDecimal(predt.Rows[0][0]) <= Convert.ToDecimal(masterdr["COSTS"]) && masterdr["PRESC_ATTR"] != DBNull.Value)
            {
                if (masterdr["PRESC_ATTR"].ToString() == "现金")
                {
                    if (XtraMessageBox.Show("此病人的现金不足，是否继续保存？", "提示", MessageBoxButtons.OKCancel) == DialogResult.Cancel)
                    {
                        return -1;
                    }
                }
            }
            //预交金总额, 透支额度 未结算费用 预交金余额(根据参数设置, 可能包含透支额度) 可用预交金总额(根据参数设置, 可能包含透支额度)
            decimal ldc_prepayments = 0;
            decimal ldc_approve = 0;
            decimal ldc_charge = 0;
            decimal ldc_prepaybalan = 0;
            decimal ldc_ldprepayment = 0;
            PlatCommon.Common.PublicFunction.GetPrepayment("*", patientid, visitid.ToString(), ref ldc_prepayments, ref ldc_approve, ref ldc_charge, ref ldc_prepaybalan, ref ldc_ldprepayment);
            //住院处方发药时，预交金不足是否允许发药:0不允许 1允许
            string confirm = PlatCommon.SysBase.SystemParm.GetParameterValue("CONFIRM", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string chargetype1 = masterdr["CHARGE_TYPE"].ToString();
            //if (PlatCommon.Common.PublicFunction.UnCheckPrepayment(patientid, visitid.ToString()) == 0)//2022-5-18 应宜宾六院住院西药房要求，预交金不足不进行提示
            //{
            //    if (confirm == "0")
            //    {
            //        if (chargetypes.Contains(chargetype1))
            //        {
            //            if (ldc_prepaybalan < Convert.ToDecimal(masterdr["PAYMENTS"]))
            //            {
            //                XtraMessageBox.Show("预交金不足，不能发药!", "提示");
            //                return -1;
            //            }
            //        }
            //    }
            //    else
            //    {
            //        if (ldc_prepaybalan < Convert.ToDecimal(masterdr["PAYMENTS"]))
            //        {
            //            if (XtraMessageBox.Show("此病人的现金不足，是否继续保存？", "提示", MessageBoxButtons.OKCancel) == DialogResult.Cancel)
            //            {
            //                return -1;
            //            }
            //        }
            //    }
            //}
            decimal stockcount;//库存数量
            decimal detailcount;//当前使用数量
            decimal onecount;//每次减的数量，第一条库存不够，为第一条数量，第一条够了，为使用数量
            string insertsql = "";
            masterdr["DISPENSING_USERCODE"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
            masterdr["DISPENSING_PROVIDER"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
            masterdr["COSTS"] = prescdetadt.Compute("sum(COSTS)", "TRUE");
            masterdr["PAYMENTS"] = prescdetadt.Compute("sum(PAYMENTS)", "TRUE");

            string bedGroup = lup_SpescAttr.EditValue == null ? "" : lup_SpescAttr.EditValue.ToString();
            masterdr["BED_GROUP"] = bedGroup;
            insertsql = InsertDoctDrugPrescMaster(masterdr, batchno, this.DeptCode);
            if (!tempsave.ContainsKey(insertsql))
            {
                //allsave.Add(insertsql, "新增住院处方主记录失败！");//改为暂时保存，防止中途库存不足退出，插入主表数据，而且没有明细表
                tempsave.Add(insertsql, "新增住院处方主记录失败！");
            }
            DataTable stockdt = new DataTable();//药品库存
            DataTable insertdetaildt = GetPrescDetailDataRow();
            //更新明细记录
            int li_item_no = 0;
            for (int i = 0; i < prescdetadt.Rows.Count; i++)
            {
                stockdt = GetDrugStockForUpdate(this.DeptCode, prescdetadt.Rows[i]["DRUG_CODE"].ToString(), prescdetadt.Rows[i]["DRUG_SPEC"].ToString(), prescdetadt.Rows[i]["PACKAGE_SPEC"].ToString(), prescdetadt.Rows[i]["FIRM_ID"].ToString());
                if (stockdt.Rows.Count == 0)
                {
                    //if (XtraMessageBox.Show("药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存不够,是否继续摆药？！", "提示", MessageBoxButtons.OKCancel) == DialogResult.OK)
                    //    continue;
                    //else
                    //    return -1;//如果继续执行，会出现只插入主表的情况 by lions 2019-05-23
                    XtraMessageBox.Show("未发现药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存,不能发药！", "提示", MessageBoxButtons.OK);
                    return -1;
                }
                detailcount = Convert.ToDecimal(prescdetadt.Rows[i]["QUANTITY"]);
                stockcount = Convert.ToDecimal(stockdt.Compute("sum(QUANTITY)", "true"));//多条库存总数量
                if (stockcount < detailcount)
                {
                    //if (XtraMessageBox.Show("药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存数量不够,是否继续摆药？！", "提示", MessageBoxButtons.OKCancel) == DialogResult.OK)
                    //    continue;
                    //else
                    //    return -1;
                    XtraMessageBox.Show("药品" + prescdetadt.Rows[i]["DRUG_NAME"] + "的库存【" + stockcount + "】数量不够[" + detailcount + "],不能发药！", "提示", MessageBoxButtons.OK);
                    return -1;
                }
                decimal decINVENTORY = 0;
                //分批减库存
                for (int j = 0; j < stockdt.Rows.Count; j++)
                {
                    if (detailcount == 0) break;
                    li_item_no = li_item_no + 1;
                    if (Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]) >= detailcount)//一条库存够减的情况
                    {
                        onecount = detailcount;
                        insertsql = UpdateDrugStock(onecount, this.DeptCode, prescdetadt.Rows[i]["DRUG_CODE"].ToString(), prescdetadt.Rows[i]["DRUG_SPEC"].ToString(), prescdetadt.Rows[i]["PACKAGE_SPEC"].ToString(), prescdetadt.Rows[i]["FIRM_ID"].ToString(), stockdt.Rows[j]["BATCH_NO"].ToString());
                        detailcount = 0;
                        decINVENTORY = Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]) - onecount;
                    }
                    else
                    {
                        detailcount = detailcount - Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]);
                        onecount = Convert.ToDecimal(stockdt.Rows[j]["QUANTITY"]);
                        insertsql = UpdateDrugStock(onecount, this.DeptCode, stockdt.Rows[j]["DRUG_CODE"].ToString(), stockdt.Rows[j]["DRUG_SPEC"].ToString(), stockdt.Rows[j]["PACKAGE_SPEC"].ToString(), stockdt.Rows[j]["FIRM_ID"].ToString(), stockdt.Rows[j]["BATCH_NO"].ToString());
                        decINVENTORY = 0;
                    }
                    //if (!allsave.ContainsKey(insertsql))
                    //{
                    //    allsave.Add(insertsql, "修改药品库存数量失败！");
                    //}
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "修改药品库存数量失败！");
                    }
                    DataRow detadr = insertdetaildt.NewRow();
                    detadr["PRESC_DATE"] = prescdetadt.Rows[i]["PRESC_DATE"];
                    detadr["PRESC_NO"] = prescdetadt.Rows[i]["PRESC_NO"];
                    detadr["ITEM_NO"] = li_item_no;
                    detadr["DRUG_CODE"] = prescdetadt.Rows[i]["DRUG_CODE"];
                    detadr["DRUG_NAME"] = stockdt.Rows[j]["DRUG_NAME"];
                    detadr["TRADE_NAME"] = stockdt.Rows[j]["TRADE_NAME"];
                    detadr["DRUG_SPEC"] = prescdetadt.Rows[i]["DRUG_SPEC"];
                    detadr["UNITS"] = prescdetadt.Rows[i]["UNITS"];
                    detadr["PACKAGE_SPEC"] = prescdetadt.Rows[i]["PACKAGE_SPEC"];
                    detadr["PACKAGE_UNITS"] = prescdetadt.Rows[i]["PACKAGE_UNITS"];
                    detadr["FIRM_ID"] = prescdetadt.Rows[i]["FIRM_ID"];
                    detadr["BATCH_NO"] = stockdt.Rows[j]["BATCH_NO"];
                    detadr["GEN_SERIAL"] = GetGenSerialSeq();
                    detadr["QUANTITY"] = onecount;
                    detadr["INVENTORY"] = decINVENTORY;
                    detadr["BATCH_CODE"] = stockdt.Rows[j]["BATCH_CODE"];
                    detadr["EXPIRE_DATE"] = stockdt.Rows[j]["EXPIRE_DATE"] == DBNull.Value ? "" : Convert.ToDateTime(stockdt.Rows[j]["EXPIRE_DATE"]).ToString("yyyy-MM-dd");
                    detadr["PURCHASE_PRICE"] = stockdt.Rows[j]["PURCHASE_PRICE"];
                    detadr["TRADE_PRICE"] = stockdt.Rows[j]["TRADE_PRICE"];
                    detadr["RETAIL_PRICE"] = stockdt.Rows[j]["RETAIL_PRICE"];
                    detadr["SUPPLIER"] = stockdt.Rows[j]["SUPPLIER"];
                    detadr["COSTS"] = (Convert.ToDecimal(stockdt.Rows[j]["RETAIL_PRICE"])) * onecount; // (Convert.ToDecimal(prescdetadt.Rows[i]["RETAIL_PRICE"])) * onecount;
                    detadr["PAYMENTS"] = (Convert.ToDecimal(stockdt.Rows[j]["RETAIL_PRICE"])) * onecount; // (Convert.ToDecimal(prescdetadt.Rows[i]["RETAIL_PRICE"])) * onecount;
                    detadr["ROUND_AMT"] = prescdetadt.Rows[i]["ROUND_AMT"];
                    detadr["ORDER_NO"] = prescdetadt.Rows[i]["ORDER_NO"];
                    detadr["ORDER_SUB_NO"] = prescdetadt.Rows[i]["ORDER_SUB_NO"];
                    detadr["ADMINISTRATION"] = prescdetadt.Rows[i]["ADMINISTRATION"];
                    detadr["DOSAGE_EACH"] = prescdetadt.Rows[i]["DOSAGE_EACH"];
                    detadr["DOSAGE_UNITS"] = prescdetadt.Rows[i]["DOSAGE_UNITS"];
                    detadr["FREQUENCY"] = prescdetadt.Rows[i]["FREQUENCY"];
                    detadr["FREQ_DETAIL"] = prescdetadt.Rows[i]["FREQ_DETAIL"];
                    detadr["INSUR_ADULT"] = prescdetadt.Rows[i]["INSUR_ADULT"];
                    detadr["HANDBACK_AMOUNT"] = 0;
                    detadr["GUID"] = stockdt.Rows[j]["GUID"];
                    insertsql = InsertDoctDrugPrescDetail(detadr);
                    //if (!allsave.ContainsKey(insertsql))
                    //{
                    //    allsave.Add(insertsql, "新增住院处方明细记录失败！");
                    //}
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "新增住院处方明细记录失败！");
                    }
                    insertdetaildt.Rows.Add(detadr);
                    stockcount = stockcount - onecount;

                    //发药和摆药为同种药品时，由于先取库存影响明细结存数不准，插入明细后再次根据当前库存更新结存数
                    insertsql = @"update DRUG_PRESC_DETAIL set INVENTORY=(select d.quantity from drug_stock d where d.storage='" + this.DeptCode + "' " +
                        "and d.drug_code = '" + prescdetadt.Rows[i]["DRUG_CODE"] + "' " +
                        "and d.drug_spec = '" + prescdetadt.Rows[i]["DRUG_SPEC"] + "' " +
                        "and d.firm_id = '" + prescdetadt.Rows[i]["FIRM_ID"] + "' " +
                        "and d.package_spec = '" + prescdetadt.Rows[i]["PACKAGE_SPEC"] + "' " +
                        "and d.batch_no = '" + stockdt.Rows[j]["BATCH_NO"] + "') where DRUG_PRESC_DETAIL.Presc_Date =to_date('" + prescdetadt.Rows[i]["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss')" +
                                                                                        "and DRUG_PRESC_DETAIL.Presc_No = " + prescdetadt.Rows[i]["PRESC_NO"] +
                                                                                        "and DRUG_PRESC_DETAIL.Item_No =" + li_item_no;
                    if (!tempsave.ContainsKey(insertsql))
                    {
                        tempsave.Add(insertsql, "更新住院处方结存数失败！");
                    }
                }

                int resultindex = InsertInpBill(insertdetaildt, ref allsave, i);
                insertdetaildt.Rows.Clear();
                if (resultindex < 0) return -1;
            }
            //insertsql = UpdateDoctPrescMasterByPrescDateAndNo(SystemParm.LoginUser.USER_NAME, SystemParm.Deptcode, Convert.ToDateTime(masterdr["PRESC_DATE"]).ToString(), masterdr["PRESC_NO"].ToString());
            //if (!allsave.ContainsKey(insertsql))
            //{
            //    allsave.Add(insertsql, "修改住院处方主记录失败！");
            //}
            if (masterdr["PRESC_TYPE"].ToString() == "1")//中药需要煎药， 插入病人煎药费
            {
                //是否收取煎药费 1收取 0不收
                string decoct = PlatCommon.SysBase.SystemParm.GetParameterValue("DECOCT_INDICATOR", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (decoct == "1")
                {
                    //if (masterdr["DECOCTION"] != DBNull.Value && masterdr["DECOCTION"].ToString() == "1")
                    //{
                    //    //煎药费在价表中的代码
                    //    string decoctcode = PlatCommon.SysBase.SystemParm.GetParameterValue("DECOCT_CODE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    //    if (decoctcode == "")
                    //    {
                    //        XtraMessageBox.Show("煎药代码无效！", "提示");
                    //        return -1;
                    //    }
                    //    else
                    //    {
                    //        int right = InsertDecoctIndicator(ref tempsave);//InsertDecoctIndicator(ref allsave);
                    //        if (right < 0) return -1;
                    //    }
                    //}

                    if (masterdr["DECOCTION"] != DBNull.Value)
                    {
                        string DecoctionCode = getDecocotCodeBySerial(masterdr["DECOCTION"].ToString());
                        if (DecoctionCode != "")
                            InsertDecoctIndicator(ref allsave);
                    }
                }
            }
            insertsql = UpdateDoctPrescMasterByPrescDateAndNo(PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, this.DeptCode, Convert.ToDateTime(masterdr["PRESC_DATE"]).ToString(), masterdr["PRESC_NO"].ToString());
            //if (!allsave.ContainsKey(insertsql))
            //{
            //    allsave.Add(insertsql, "修改住院处方主记录状态失败！");
            //}
            if (!tempsave.ContainsKey(insertsql))
            {
                tempsave.Add(insertsql, "修改住院处方主记录状态失败！");
            }
            //最终存入保存的sql集合
            for (int ip = 0; ip < tempsave.Count; ip++)
            {
                allsave.Add(tempsave.ElementAt(ip).Key, tempsave.ElementAt(ip).Value);
            }
            return 1;
        }
        private string getDecocotCodeBySerial(string serial)
        {
            string sql = "select item_code from OUTPDOCT.V_STATIC_DECOCTION_VS_CHARGE where serial_no='" + serial + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            return spc.ExecuteScalarStr(sql);
        }

        /// <summary>
        /// 插入病人费用明细
        /// </summary>
        private int InsertInpBill(DataTable detadr1, ref Dictionary<string, string> allsave, int index)
        {
            if (masterdr == null) return -1;
            //非住院处方,直接返回
            if (masterdr["PRESC_SOURCE"] == DBNull.Value || masterdr["PRESC_SOURCE"].ToString() != "1") return -1;
            object repetition = masterdr["REPETITION"] != DBNull.Value ? masterdr["REPETITION"] : 1;
            DataTable doctordt = new DataTable();
            string doctorname = "";
            string doctorgroup = "";
            string doctoruser = "";
            string orderdept = "";//科室
            int maxitemno = 0;//住院收费明细inp_bill_detail的最大项目序号
            doctordt = GetDoctorGroupByDoctorUser(masterdr["DOCTOR_USER"].ToString());
            if (doctordt.Rows.Count == 0)
            {
                doctordt = GetOrderGroupRecByPatientID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (doctordt.Rows.Count == 0)
                {
                    doctorname = "*";
                    doctorgroup = "*";
                }
                else
                {
                    doctorname = doctordt.Rows[0]["order_doctor"].ToString();
                    doctorgroup = doctordt.Rows[0]["order_group"].ToString();
                }
            }
            else
            {
                doctorname = doctordt.Rows[0]["doctor"].ToString();
                doctorgroup = doctordt.Rows[0]["order_group"].ToString();
            }
            //取开单科室 先取doct表里开单，如果没有，取患者所在科室 2019-05-15
            orderdept = masterdr["ORDERED_BY"].ToString();
            if (string.IsNullOrEmpty(orderdept))
            {
                DataTable deptdt = new DataTable();
                deptdt = GetPatInHospDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (deptdt.Rows.Count == 0)
                {
                    deptdt = GetPatVisitDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                    if (deptdt.Rows.Count > 0)
                    {
                        orderdept = deptdt.Rows[0]["dept_discharge_from"].ToString();
                    }
                    else
                    {
                        XtraMessageBox.Show("取该病人开单科室出错！", "提示");
                        return -1;
                    }
                }
                else
                {
                    if (deptdt.Rows[0]["dept_code"] != DBNull.Value)
                    {
                        orderdept = deptdt.Rows[0]["dept_code"].ToString();
                    }
                    else
                    {
                        orderdept = PlatCommon.SysBase.SystemParm.LoginUser.DEPT_CODE;//找不到开单科室，取医生所属
                    }
                }
            }


            string lsorderbydept = "";
            string orderedby = PlatCommon.SysBase.SystemParm.GetParameterValue("ORDERED_BY", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            //DataTable deptsdt = GetDeptCodeByUserName(masterdr["doctor_user"].ToString());
            //if (deptdt.Rows.Count > 0)
            //{
            //    if (deptsdt.Rows[0]["dept_code"].ToString().Contains(orderedby))
            //    {
            //        orderdept = deptsdt.Rows[0]["dept_code"].ToString();
            //    }
            //}

            string prescdate = masterdr["PRESC_DATE"].ToString();
            string prescno = masterdr["PRESC_NO"].ToString();
            int takingind = masterdr["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value ? Convert.ToInt16(masterdr["DISCHARGE_TAKING_INDICATOR"]) : -1;//带药标志
            string itemclass = masterdr["PRESC_TYPE"].ToString() == "0" ? "A" : "B";
            string verifieddatetime = "";
            string verifyby = "";
            DataTable verifydt = GetPrescMasterByNoAndDate(prescno, prescdate);
            if (verifydt.Rows.Count > 0)
            {
                verifieddatetime = verifydt.Rows[0]["verified_datetime"] != DBNull.Value ? verifydt.Rows[0]["verified_datetime"].ToString() : "";
                verifyby = verifydt.Rows[0]["verify_by"] != DBNull.Value ? verifydt.Rows[0]["verify_by"].ToString() : "";
            }

            DataTable maxitemnodt = GetMaxItemNoByPatientIDAndVisitID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (maxitemnodt.Rows.Count > 0)
            {
                if (string.IsNullOrEmpty(maxitemnodt.Rows[0][0].ToString()))
                {
                    maxitemno = 1;
                }
                else
                {
                    maxitemno = Convert.ToInt16(maxitemnodt.Rows[0][0]) + index + 1;
                }

            }




            foreach (DataRow detadr in detadr1.Rows)
            {

                //记录item_no防止主键冲突item_no
                if (list_item.Count > 0)
                {
                    if (list_item.ContainsKey(masterdr["PATIENT_ID"].ToString()))
                    {

                        string ls_item_no = list_item[masterdr["PATIENT_ID"].ToString()];
                        ls_item_no = (int.Parse(ls_item_no) + 1).ToString();
                        list_item[masterdr["PATIENT_ID"].ToString()] = ls_item_no;
                        maxitemno = int.Parse(ls_item_no);
                    }
                    else
                    {
                        list_item.Add(masterdr["PATIENT_ID"].ToString(), maxitemno.ToString());
                    }
                }
                else
                {
                    list_item.Add(masterdr["PATIENT_ID"].ToString(), maxitemno.ToString());
                }
                //记录item_no


                string order_no = detadr["ORDER_NO"].ToString();
                decimal ldec_costs = Convert.ToDecimal(detadr["costs"]);
                decimal ldec_payments = Convert.ToDecimal(detadr["payments"]);
                decimal factor = 1;
                if (ldec_costs == 0)
                {
                    factor = 0;
                }
                else
                {
                    factor = Math.Round(ldec_payments / ldec_costs, 4);
                }
                string dispensingtime = masterdr["DISPENSING_DATETIME"] == DBNull.Value ? GetSystemTime().ToString("yyyy-MM-dd HH:mm:ss") : masterdr["DISPENSING_DATETIME"].ToString();
                detadr["firm_id"] = detadr["firm_id"] == DBNull.Value ? "" : detadr["firm_id"].ToString();
                DataTable priceclassdt = GetPriceListClassByItem(itemclass, detadr["DRUG_CODE"].ToString(), detadr["package_spec"].ToString() + detadr["firm_id"].ToString());
                if (priceclassdt.Rows.Count < 1)
                {
                    XtraMessageBox.Show("取不到PRICE_LIST表中字段信息!", "提示");
                    return -1;
                }
                string classonmr = priceclassdt.Rows[0]["class_on_mr"] == DBNull.Value ? "" : priceclassdt.Rows[0]["class_on_mr"].ToString();
                DataTable doctgroupdt = GetDoctorGroupByName(doctorname);
                string personggourp = "*";
                if (doctgroupdt.Rows.Count > 0)
                {
                    personggourp = doctgroupdt.Rows[0]["order_group"] != DBNull.Value ? doctgroupdt.Rows[0]["order_group"].ToString() : "*";
                }


                //string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString()(as_patient_id), masterdr["VISIT_ID"].ToString()(al_visit_id), maxitemno.ToString()(al_item_no), itemclass(as_item_class), priceclassdt.Rows[0]["item_name"].ToString()(ls_item_name), detadr["DRUG_CODE"].ToString()(as_item_code), detadr["package_spec"].ToString() + detadr["firm_id"].ToString()(as_item_spec), detadr["quantity"].ToString()(adec_amount), detadr["package_units"].ToString()(as_item_units), orderdept(as_order_by), SystemParm.Deptcode(as_performed_by), Math.Round(detadr["costs"], 4).ToString()(ldec_cur_costs), Math.Round(detadr["payments"], 4).ToString()(ldec_cur_charges), dispensingtime(ldt_date), SystemParm.LoginUser.EMP_NO(as_operator_code), priceclassdt.Rows[0]["class_on_reckoning"].ToString()(ls_class_on_reckoning)(), doctorgroup(as_order_group), doctorname(as_order_name), personggourp(ls_perform_group), doctorname(as_perform_name), masterdr["doctor_user"].ToString()(as_order_doctor_code), decoctdt.Rows[0]["price"].ToString()(adec_price), factor.ToString()(ldec_factor), takingind.ToString()(al_discharge_taking), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString()(ls_class_on_inp_rcpt), priceclassdt.Rows[0]["subj_code"].ToString()(ls_subj_code), classonmr(ls_class_on_mr), ""(as_rcpt_no), "A"(as_oper_type), Convert.ToDateTime(prescdate).ToString("yyyymmdd") + prescno + ","(as_oper_code), ""(as_memo), batchno(as_batch_no));
                //string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString(), maxitemno.ToString(), itemclass, priceclassdt.Rows[0]["item_name"].ToString(), detadr["DRUG_CODE"].ToString(), detadr["package_spec"].ToString() + detadr["firm_id"].ToString(), detadr["quantity"].ToString(), detadr["package_units"].ToString(), orderdept, SystemParm.Deptcode, Math.Round(Convert.ToDecimal(detadr["costs"]), 4).ToString(), Math.Round(Convert.ToDecimal(detadr["payments"]), 4).ToString(), dispensingtime, SystemParm.LoginUser.EMP_NO, priceclassdt.Rows[0]["class_on_reckoning"].ToString(), doctorgroup, doctorname, personggourp, doctorname, masterdr["doctor_user"].ToString(), detadr["retail_price"].ToString(), factor.ToString(), takingind.ToString(), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString(), priceclassdt.Rows[0]["subj_code"].ToString(), classonmr, "", "A", Convert.ToDateTime(prescdate).ToString("yyyymmdd") + prescno + ",", "", detadr["batch_no"].ToString());
                string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString(), maxitemno.ToString(),
                    itemclass, priceclassdt.Rows[0]["item_name"].ToString(), detadr["DRUG_CODE"].ToString(),
                    detadr["package_spec"].ToString() + detadr["firm_id"].ToString(), detadr["quantity"].ToString(),
                    detadr["package_units"].ToString(), orderdept, this.DeptCode, Math.Round(Convert.ToDecimal(detadr["costs"]), 2).ToString(),
                    Math.Round(Convert.ToDecimal(detadr["payments"]), 2).ToString(), dispensingtime,
                    PlatCommon.SysBase.SystemParm.LoginUser.EMP_NO, priceclassdt.Rows[0]["class_on_reckoning"].ToString(),
                    doctorgroup, doctorname, personggourp, doctorname, masterdr["doctor_user"].ToString(), detadr["retail_price"].ToString(),
                    factor.ToString(), takingind.ToString(), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString(), priceclassdt.Rows[0]["subj_code"].ToString(),
                    classonmr, "", "A", order_no + "", "", detadr["batch_no"].ToString(), detadr["TRADE_PRICE"] != DBNull.Value ? detadr["TRADE_PRICE"].ToString() : "0", "0",
                     detadr["BATCH_CODE"].ToString(), detadr["GUID"].ToString());
                if (!allsave.ContainsKey(insertsql))
                {
                    allsave.Add(insertsql, "添加住院患者费用明细记录失败！");
                }
            }
            return 1;
        }
        /// <summary>
        /// 插入煎药费
        /// </summary>
        private int InsertDecoctIndicator(ref Dictionary<string, string> allsave)
        {
            if (masterdr == null) return -1;
            //非住院处方,直接返回
            if (masterdr["PRESC_SOURCE"] == DBNull.Value || masterdr["PRESC_SOURCE"].ToString() != "1") return -1;
            object repetition = masterdr["REPETITION"] != DBNull.Value ? masterdr["REPETITION"] : 1;
            DataTable doctordt = new DataTable();
            string doctorname = "";
            string doctorgroup = "";
            string doctoruser = "";
            string orderdept = "";//科室
            int maxitemno = 0;//住院收费明细inp_bill_detail的最大项目序号
            doctordt = GetDoctorGroupByDoctorUser(masterdr["DOCTOR_USER"].ToString());
            if (doctordt.Rows.Count == 0)
            {
                doctordt = GetOrderGroupRecByPatientID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (doctordt.Rows.Count == 0)
                {
                    doctorname = "*";
                    doctorgroup = "*";
                }
                else
                {
                    doctorname = doctordt.Rows[0]["order_doctor"].ToString();
                    doctorgroup = doctordt.Rows[0]["order_group"].ToString();
                }
            }
            else
            {
                doctorname = doctordt.Rows[0]["doctor"].ToString();
                doctorgroup = doctordt.Rows[0]["order_group"].ToString();
            }
            DataTable deptdt = new DataTable();
            deptdt = GetPatInHospDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (deptdt.Rows.Count == 0)
            {
                deptdt = GetPatVisitDeptCode(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
                if (deptdt.Rows.Count > 0)
                {
                    orderdept = deptdt.Rows[0]["dept_discharge_from"].ToString();
                }
                else
                {
                    XtraMessageBox.Show("取该病人开单科室出错！", "提示");
                    return -1;
                }
            }
            else
            {
                if (deptdt.Rows[0]["dept_code"] != DBNull.Value)
                {
                    orderdept = deptdt.Rows[0]["dept_code"].ToString();
                }
            }
            string prescdate = masterdr["PRESC_DATE"].ToString();
            string prescno = masterdr["PRESC_NO"].ToString();
            int takingind = masterdr["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value ? Convert.ToInt16(masterdr["DISCHARGE_TAKING_INDICATOR"]) : -1;//带药标志
            //string itemclass = masterdr["PRESC_TYPE"].ToString() == "0" ? "A" : "B";
            string itemclass = masterdr["PRESC_TYPE"].ToString() == "0" ? "A" : "Z";
            string verifieddatetime = "";
            string verifyby = "";
            DataTable verifydt = GetPrescMasterByNoAndDate(prescno, Convert.ToDateTime(prescdate).ToString("yyyy-MM-dd"));
            if (verifydt.Rows.Count > 0)
            {
                verifieddatetime = verifydt.Rows[0]["verified_datetime"] != DBNull.Value ? verifydt.Rows[0]["verified_datetime"].ToString() : "";
                verifyby = verifydt.Rows[0]["verify_by"] != DBNull.Value ? verifydt.Rows[0]["verify_by"].ToString() : "";
            }
            DataTable maxitemnodt = GetMaxItemNoByPatientIDAndVisitID(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString());
            if (maxitemnodt.Rows.Count > 0)
            {
                maxitemno = Convert.ToInt16(maxitemnodt.Rows[0][0]);
            }
            //string decoctcode = PlatCommon.SysBase.SystemParm.GetParameterValue("DECOCT_CODE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            //if (decoctcode == "")
            //{
            //    XtraMessageBox.Show("煎药代码无效!", "提示");
            //    return -1;
            //}
            string decoctcode = getDecocotCodeBySerial(masterdr["DECOCTION"].ToString());

            DataTable decoctdt = GetPriceListByDecocotCode(decoctcode);//煎药价表信息
            if (decoctdt.Rows.Count == 0)
            {
                XtraMessageBox.Show("煎药代码对应的价格未设置！", "提示");
                return -1;
            }
            else
            {
                if (decoctdt.Rows[0]["price"] == DBNull.Value)
                {
                    XtraMessageBox.Show("煎药代码对应的价格未知！请查证！", "提示");
                    return -1;
                }
            }
            DataTable coeffnumerator = GetChargePriceSchedule(masterdr["CHARGE_TYPE"].ToString(), PlatCommon.SysBase.SystemParm.HisUnitCode);
            decimal decDefault = 1;
            if (coeffnumerator.Rows.Count > 0)
            {
                decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
            }
            decimal decPrice = 0.00m;
            PlatCommon.Common.PublicFunction.GetCalcChargePrice(masterdr["CHARGE_TYPE"].ToString(), itemclass, decoctcode, decoctdt.Rows[0]["item_spec"].ToString(), Convert.ToDecimal(decoctdt.Rows[0]["price"]), ref decDefault, ref decPrice);

            string batchno = "X";
            string batchcode = "X";
            int repetion = Convert.ToInt16(masterdr["REPETITION"]);
            decimal ldec_costs = repetion * Convert.ToDecimal(decoctdt.Rows[0]["price"]);
            decimal ldec_payments = repetion * decPrice;
            decimal factor = 1;
            factor = Math.Round(ldec_payments / ldec_costs, 4);
            string dispensingtime = masterdr["DISPENSING_DATETIME"] == DBNull.Value ? GetSystemTime().ToString("yyyy-MM-dd HH:mm:ss") : masterdr["DISPENSING_DATETIME"].ToString();

            DataTable priceclassdt = GetPriceListClassByItem(itemclass, itemclass, decoctdt.Rows[0]["item_spec"].ToString());
            if (priceclassdt.Rows.Count < 1)
            {
                XtraMessageBox.Show("取不到PRICE_LIST表中字段信息!", "提示");
                return -1;
            }
            string classonmr = priceclassdt.Rows[0]["class_on_mr"] == DBNull.Value ? "" : priceclassdt.Rows[0]["class_on_mr"].ToString();
            DataTable doctgroupdt = GetDoctorGroupByName(doctorname);
            string personggourp = "*";
            if (doctgroupdt.Rows.Count > 0)
            {
                personggourp = doctgroupdt.Rows[0]["order_group"] == DBNull.Value ? doctgroupdt.Rows[0]["order_group"].ToString() : "*";
            }
            string insertsql = InsertInpBillDetail(masterdr["PATIENT_ID"].ToString(), masterdr["VISIT_ID"].ToString(), maxitemno.ToString(),
                itemclass, decoctdt.Rows[0]["item_name"].ToString(), decoctcode, decoctdt.Rows[0]["item_spec"].ToString(), repetion.ToString(),
                decoctdt.Rows[0]["units"].ToString(), orderdept, this.DeptCode, Math.Round(ldec_costs, 2).ToString(), Math.Round(ldec_payments, 2).ToString(),
                dispensingtime, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME, priceclassdt.Rows[0]["class_on_reckoning"].ToString(), doctorgroup,
                doctorname, personggourp, doctorname, masterdr["doctor_user"].ToString(), decoctdt.Rows[0]["price"].ToString(),
                factor.ToString(), takingind.ToString(), priceclassdt.Rows[0]["class_on_inp_rcpt"].ToString(), priceclassdt.Rows[0]["subj_code"].ToString(),
                classonmr, "", "A", Convert.ToDateTime(prescdate).ToString("yyyymmdd") + prescno, "", batchno, "", "0",
                batchcode, "");
            if (!allsave.ContainsKey(insertsql))
            {
                allsave.Add(insertsql, "添加住院患者费用明细记录失败！");
            }
            return 1;
        }
        /// <summary>
        /// 初始化处方明细列名
        /// </summary>
        /// <returns></returns>
        private DataTable GetPrescDetailDataRow()
        {
            DataTable detaildt = new DataTable();
            detaildt.Columns.Add("PRESC_DATE");
            detaildt.Columns.Add("PRESC_NO");
            detaildt.Columns.Add("ITEM_NO");
            detaildt.Columns.Add("DRUG_CODE");
            detaildt.Columns.Add("DRUG_NAME");
            detaildt.Columns.Add("TRADE_NAME");
            detaildt.Columns.Add("DRUG_SPEC");
            detaildt.Columns.Add("UNITS");
            detaildt.Columns.Add("PACKAGE_SPEC");
            detaildt.Columns.Add("PACKAGE_UNITS");
            detaildt.Columns.Add("FIRM_ID");
            detaildt.Columns.Add("BATCH_NO");
            detaildt.Columns.Add("GEN_SERIAL");
            detaildt.Columns.Add("QUANTITY");
            detaildt.Columns.Add("INVENTORY");
            detaildt.Columns.Add("BATCH_CODE");
            detaildt.Columns.Add("EXPIRE_DATE");
            detaildt.Columns.Add("PURCHASE_PRICE");
            detaildt.Columns.Add("TRADE_PRICE");
            detaildt.Columns.Add("RETAIL_PRICE");
            detaildt.Columns.Add("SUPPLIER");
            detaildt.Columns.Add("COSTS");
            detaildt.Columns.Add("PAYMENTS");
            detaildt.Columns.Add("ROUND_AMT");
            detaildt.Columns.Add("ORDER_NO");
            detaildt.Columns.Add("ORDER_SUB_NO");
            detaildt.Columns.Add("ADMINISTRATION");
            detaildt.Columns.Add("DOSAGE_EACH");
            detaildt.Columns.Add("DOSAGE_UNITS");
            detaildt.Columns.Add("FREQUENCY");
            detaildt.Columns.Add("FREQ_DETAIL");
            detaildt.Columns.Add("HANDBACK_AMOUNT");
            detaildt.Columns.Add("INSUR_ADULT");
            detaildt.Columns.Add("GUID");
            return detaildt;
        }
        /// <summary>
        /// 清楚基础信息
        /// </summary>
        private void CleanTxt()
        {
            txtPatientID.Text = "";
            txtName.Text = "";
            txtAge.Text = "";
            txtSex.Text = "";
            txtIdentity.Text = "";
            txtChargeType.Text = "";
            sluePrescAttr.EditValue = null;//处方属性
            txtPrescDate.Text = "";//开方日期
            txtPrescNo.Text = "";
            txtPrescribed.Text = "";//开单医生
            txtDeptName.Text = "";//科室
            txtYu.Text = "";//预交金
            txtJi.Text = "";//计价
            txtYing.Text = "";//应收
            txtAdmin.Text = "";//用法

            txtRepetition.Text = "";//剂数
            txtCountPerRepetition.Text = "";//每剂/份
            txtDecoction.Text = "";
            txtDischargeTaking.Text = "";
            gc2.DataSource = null;
        }
        /// <summary>
        /// 根据处方号和处方日期显示处方信息
        /// </summary>
        /// <param name="rowindex"></param>
        private void SetBaseInfoAndDetailByPrescDateAndPrescNo(int rowindex)
        {
            string prescdate = gv1.GetRowCellValue(rowindex, "PRESC_DATE").ToString();
            string prescno = gv1.GetRowCellValue(rowindex, "PRESC_NO").ToString();
            DataTable masterdt = GetDoctPrescMasterTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);
            if (masterdt.Rows.Count > 0)
            {
                txtPatientID.Text = masterdt.Rows[0]["PATIENT_ID"].ToString();
                txtName.Text = masterdt.Rows[0]["NAME"].ToString();
                txtAge.Text = masterdt.Rows[0]["AGE"].ToString();
                txtSex.Text = masterdt.Rows[0]["SEX"].ToString();
                txtIdentity.Text = masterdt.Rows[0]["IDENTITY"].ToString();
                txtChargeType.Text = masterdt.Rows[0]["CHARGE_TYPE"].ToString();
                sluePrescAttr.EditValue = masterdt.Rows[0]["PRESC_ATTR"] != DBNull.Value ? masterdt.Rows[0]["PRESC_ATTR"].ToString() : "";//处方属性
                txtPrescDate.Text = masterdt.Rows[0]["PRESC_DATE"].ToString();//开方日期
                txtPrescNo.Text = masterdt.Rows[0]["PRESC_NO"].ToString();
                txtPrescribed.Text = masterdt.Rows[0]["PRESCRIBED_BY"].ToString();//开单医生
                txtDeptName.Text = masterdt.Rows[0]["ORDERED_BY"].ToString();//科室
                //txtPrescType.Text = masterdt.Rows[0]["ORDERED_BY"].ToString() == "1" ? "中药" : "西药";//处方类别
                txtYu.Text = masterdt.Rows[0]["PREPAYMENT"].ToString();//预交金
                txtJi.Text = masterdt.Rows[0]["COSTS"].ToString();//计价
                txtYing.Text = masterdt.Rows[0]["PAYMENTS"].ToString();//应收
                txtAdmin.Text = masterdt.Rows[0]["USAGE"] != DBNull.Value ? masterdt.Rows[0]["USAGE"].ToString() : "";//用法

                txtRepetition.Text = masterdt.Rows[0]["REPETITION"].ToString();//剂树
                txtCountPerRepetition.Text = masterdt.Rows[0]["COUNT_PER_REPETITION"] != DBNull.Value ? masterdt.Rows[0]["COUNT_PER_REPETITION"].ToString() : "";//每剂/份
                // 根据DECOCTION字段值显示对应的代煎状态
                string decoctionValue = masterdt.Rows[0]["DECOCTION"].ToString();
                switch (decoctionValue)
                {
                    case "0":
                        txtDecoction.Text = "不代煎";
                        break;
                    case "1":
                        txtDecoction.Text = "人工代煎";
                        break;
                    case "2":
                        txtDecoction.Text = "机器代煎";
                        break;
                    default:
                        txtDecoction.Text = "不代煎";
                        break;
                }
                if (masterdt.Rows[0]["DISCHARGE_TAKING_INDICATOR"] != DBNull.Value)
                {
                    txtDischargeTaking.Text = masterdt.Rows[0]["DISCHARGE_TAKING_INDICATOR"].ToString() == "1" ? "出院带药" : "不带药";
                }
                else
                {
                    txtDischargeTaking.Text = "";
                }

                masterdr = masterdt.Rows[0];
            }
            else
            {
                masterdr = null;
            }
            if (masterdr != null)
            {
                string patientid = masterdr["patient_id"].ToString();
                int visitid = Convert.ToInt16(masterdr["visit_id"]);
                //判断新生儿，预交金
                DataTable newborn = CheckNewBorn(masterdr["patient_id"].ToString());
                if (newborn.Rows.Count > 0)//是新生儿,下面所有的信息都取母亲的信息
                {
                    patientid = newborn.Rows[0]["PATIENT_ID_OF_MOTHER"].ToString();
                    visitid = Convert.ToInt16(newborn.Rows[0]["VISIT_ID_OF_MOTHER"]);
                }
                //预交金总额, 透支额度 未结算费用 预交金余额(根据参数设置, 可能包含透支额度) 可用预交金总额(根据参数设置, 可能包含透支额度)
                decimal ldc_prepayments = 0;
                decimal ldc_approve = 0;
                decimal ldc_charge = 0;
                decimal ldc_prepaybalan = 0;
                decimal ldc_ldprepayment = 0;
                PlatCommon.Common.PublicFunction.GetPrepayment("*", patientid, visitid.ToString(), ref ldc_prepayments, ref ldc_approve, ref ldc_charge, ref ldc_prepaybalan, ref ldc_ldprepayment);
                txtYu.Text = ldc_prepaybalan.ToString();//预交金
                //判断年龄
                DataTable birthdt = GetBirthDayByPatientID(patientid);
                if (birthdt.Rows.Count > 0)
                {
                    if (PlatCommon.Common.PublicFunction.GetAge(Convert.ToDateTime(birthdt.Rows[0]["date_of_birth"]), Convert.ToDateTime(masterdr["PRESC_DATE"])) == string.Empty)
                    {
                        XtraMessageBox.Show("提取病人年龄出错!", "提示");
                    }
                    txtSex.Text = birthdt.Rows[0]["sex"].ToString();
                }
            }
            //设置明细信息
            DataTable detaildt = GetDoctPrescDetailTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);

            if (detaildt.Rows.Count > 0)
            {
                string chargetype = masterdr != null ? masterdr["CHARGE_TYPE"].ToString() : "";
                string presctype = masterdr != null ? masterdr["PRESC_TYPE"].ToString() : "0";
                string itemclass = presctype == "0" ? "A" : "B";
                decimal retailprice = 0;
                for (int i = 0; i < detaildt.Rows.Count; i++)
                {
                    if (detaildt.Rows[i]["FIRM_ID"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无厂家，不能发药！";
                        continue;
                    }
                    if (detaildt.Rows[i]["DRUG_SPEC"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无小规格，不能发药！";
                        continue;
                    }
                    if (detaildt.Rows[i]["package_spec"] == DBNull.Value)
                    {
                        XtraMessageBox.Show("第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！", "提示");
                        detaildt.Rows[i]["ERR_MSG"] = "第" + (i + 1) + "条项目" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "无发药规格，不能发药！";
                        continue;
                    }
                    DataTable pricedt = GetPriceListByClassCodeSpec(itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"]);
                    if (pricedt.Rows.Count > 0)
                    {
                        retailprice = Convert.ToDecimal(pricedt.Rows[0][0]);
                    }
                    //计算价格
                    DataTable coeffnumerator = GetChargePriceSchedule(chargetype, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    decimal decDefault = 1;
                    if (coeffnumerator.Rows.Count > 0)
                    {
                        decDefault = Convert.ToDecimal(coeffnumerator.Rows[0]["price_coeff_numerator"]);
                    }
                    decimal decChargePrice = 0.00m;

                    PlatCommon.Common.PublicFunction.GetCalcChargePrice(chargetype, itemclass, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["package_spec"].ToString() + detaildt.Rows[i]["FIRM_ID"], retailprice, ref decDefault, ref decChargePrice);
                    detaildt.Rows[i]["RETAIL_PRICE"] = retailprice;
                    detaildt.Rows[i]["COSTS"] = retailprice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);
                    detaildt.Rows[i]["PAYMENTS"] = decChargePrice * Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);

                }
                //显示零售价和库存
                DataTable drugstockdt = GetDrugQuantityAndStorageAndLocation(detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString(), detaildt.Rows[0]["PACKAGE_SPEC"].ToString(),
                    detaildt.Rows[0]["PACKAGE_UNITS"].ToString(), this.DeptCode);

                DataTable drugprice = GetDrugPriceDrugCode(detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["DRUG_SPEC"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
                if (drugstockdt.Rows.Count > 0)
                {
                    lbStockCount.Text = drugstockdt.Rows[0]["quantity"] != DBNull.Value ? drugstockdt.Rows[0]["quantity"].ToString() : "";
                    //lbPrice.Text = drugstockdt.Rows[0]["sub_storage"] != DBNull.Value ? drugstockdt.Rows[0]["sub_storage"].ToString() : "";
                }
                else
                {
                    lbStockCount.Text = "";
                }

            }
            else
            {
                CleanStorgeTxt();
            }
            gc2.DataSource = detaildt;

        }
        /// <summary>
        /// 设置处方属性
        /// </summary>
        private void SetPrescAttr()
        {
            DataTable prescattr = GetPrescAttr();
            sluePrescAttr.Properties.DataSource = prescattr;
            sluePrescAttr.Properties.DisplayMember = "PRESC_ATTR_NAME";
            sluePrescAttr.Properties.ValueMember = "PRESC_ATTR_NAME";
        }

        private void SetLupPrescAttr()
        {
            string prescsql = "select 0 as SERIAL_NO,'全部' as TOXI_NAME from dual";
            prescsql += " union";
            prescsql += " select SERIAL_NO,TOXI_NAME from DRUG_TOXI_PROPERTY_DICT ";
            DataTable dt = new ServerPublicClient().GetList(prescsql).Tables[0];
            lup_SpescAttr.Properties.DataSource = dt;
            lup_SpescAttr.Properties.DisplayMember = "TOXI_NAME";
            lup_SpescAttr.Properties.ValueMember = "TOXI_NAME";

        }

        /// <summary>
        /// 清空库存信息
        /// </summary>
        private void CleanStorgeTxt()
        {
            lbStockCount.Text = "";
            lbPrice.Text = "";
        }
        /// <summary>
        /// 设置科室
        /// </summary>
        private void SetDept()
        {
            DataTable deptdt = GetAllDept();
            slueDept.Properties.DataSource = deptdt;
            slueDept.Properties.DisplayMember = "DEPT_NAME";
            slueDept.Properties.ValueMember = "DEPT_CODE";
        }
        //取床位分组
        public DataTable GetBedGroup(string wardCode)
        {
            string sql = "select bed_group from bed_rec where ward_code = '" + wardCode + "' group by bed_group";
            DataTable dt = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 设置处方病人主列表
        /// </summary>
        private void SetPrescMaster()
        {
            string chargeflag = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGE_FLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string presc_verified = PlatCommon.SysBase.SystemParm.GetParameterValue("PRESC_VERIFIED", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            chargetypes = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGETYPES", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string showPrescDays = PlatCommon.SysBase.SystemParm.GetParameterValue("SHOW_PRESC_DAYS", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            DateTime starttime = DateTime.Now.AddDays(showPrescDays == "" ? -360 : -Convert.ToInt16(showPrescDays));
            DateTime endtime = DateTime.Now.AddDays(1);
            //string deptcode = slueDept.EditValue == null ? "" : slueDept.EditValue.ToString();
            // string bedGroup = lup_SpescAttr.EditValue == null ? "" : lup_SpescAttr.EditValue.ToString();
            string prescAtrr = lup_SpescAttr.EditValue == null ? "" : lup_SpescAttr.EditValue.ToString();
            DataTable prescmaster = GetDoctDrugPrescMasterByTimeAndDept(starttime, endtime, this.DeptCode, dept, prescAtrr);
            if (prescmaster != null)
            {
                DataRow[] drs;
                DataTable newmasterdt = prescmaster.Clone();
                if (cmbPrescType.Text == "西药")
                {
                    drs = prescmaster.Select("PRESC_TYPE=0");
                }
                else if (cmbPrescType.Text == "中药")
                {
                    drs = prescmaster.Select("PRESC_TYPE=1");
                }
                else
                {
                    newmasterdt = prescmaster.Copy();
                    drs = new DataRow[0];
                }
                if (drs.Length > 0)
                {
                    for (int i = 0; i < drs.Length; i++)
                    {
                        newmasterdt.Rows.Add(drs[i].ItemArray);
                    }
                }
                //if (newmasterdt.Rows.Count == 0 && cmbPrescType.Text == "") newmasterdt = prescmaster.Copy();

                gc1.DataSource = newmasterdt;

                if (newmasterdt.Rows.Count > 0)
                {
                    SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
                }
                else
                {
                    CleanTxt();
                    CleanStorgeTxt();
                }
            }
        }
        #endregion

        #region SQL
        /// <summary>
        /// 获取药品处方属性
        /// </summary>
        /// <returns></returns>
        private DataTable GetPrescAttr()
        {
            string sql = "SELECT D.SERIAL_NO,D.PRESC_ATTR_CODE,D.PRESC_ATTR_NAME  FROM DRUG_PRESC_ATTR_DICT D";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 删除门诊待发药处方明细表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescDetailTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_detail_temp	WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            return sql;
        }
        /// <summary>
        /// 删除门诊待发药处方主表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescMasterTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_master_temp	WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            return sql;
        }
        /// <summary>
        /// 获取打印数据
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescByPrint1(string prescdate, string prescno)
        {
            string sql = "SELECT M.PRESC_DATE,M.PRESC_NO,M.AGE,M.SEX,M.DISPENSARY,BR.BED_LABEL,'" + this.DeptName + "' as DISPENSARY_NAME,M.PATIENT_ID,M.NAME||' '||BR.BED_LABEL||'床' AS NAME,M.IDENTITY,M.CHARGE_TYPE,M.UNIT_IN_CONTRACT,M.PRESC_SOURCE,M.REPETITION,M.ORDERED_BY,M.PRESCRIBED_BY,D.ITEM_NO,D.DRUG_NAME,D.FIRM_ID,D.PACKAGE_SPEC,D.PACKAGE_UNITS,D.QUANTITY,M.NAME_PHONETIC,M.PRESC_TYPE,M.PRESC_ATTR,D.COSTS DCOSTS,D.PAYMENTS,M.ENTERED_BY,D.ADMINISTRATION,D.ORDER_NO,D.ORDER_SUB_NO,D.DRUG_CODE,D.DRUG_SPEC,M.DISPENSING_PROVIDER,'' Constrained_level_name,M.COSTS,P.sex,P.DATE_OF_BIRTH,D.DOSAGE_EACH,D.DOSAGE_UNITS,D.FREQUENCY,M.RCPT_NO,M.verify_by,D.DRUG_NAME || ' ' || D.QUANTITY || D.PACKAGE_UNITS bzxx,T.DEPT_NAME,trunc(D.COSTS/D.QUANTITY,2) DACOSTS  FROM DRUG_PRESC_DETAIL D, DRUG_PRESC_MASTER M, PAT_MASTER_INDEX P,DEPT_DICT T,PATS_IN_HOSPITAL PIH,BED_REC BR WHERE (D.PRESC_NO = M.PRESC_NO) and (D.PRESC_DATE = M.PRESC_DATE) and M.patient_id = P.patient_id(+)  AND (M.ORDERED_BY=T.DEPT_CODE) AND (M.PATIENT_ID=PIH.PATIENT_ID AND M.VISIT_ID=PIH.VISIT_ID ) and PIH.WARD_CODE=BR.WARD_CODE AND PIH.BED_NO=BR.BED_NO AND ((D.PRESC_DATE = to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss')) AND (D.PRESC_NO = :presc_no)) and D.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取打印数据
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescByPrint(string prescdate, string prescno)
        {
            //新加发药药房名称 by lions 2019-05-22 按床标号排序 桓仁
            string sql = "SELECT M.PRESC_DATE,M.PRESC_NO,M.AGE,M.SEX,M.DISPENSARY,BR.BED_LABEL,'" + this.DeptName + "' as DISPENSARY_NAME,M.PATIENT_ID,M.NAME||' '||BR.BED_LABEL||'床' AS NAME,M.IDENTITY,M.CHARGE_TYPE,M.UNIT_IN_CONTRACT,M.PRESC_SOURCE,M.REPETITION,M.ORDERED_BY,M.PRESCRIBED_BY,M.VISIT_ID,D.ITEM_NO,D.DRUG_NAME,D.FIRM_ID,D.PACKAGE_SPEC,D.PACKAGE_UNITS,D.QUANTITY,M.NAME_PHONETIC,M.PRESC_TYPE,M.COSTS,M.PAYMENTS,M.ENTERED_BY,M.COUNT_PER_REPETITION,D.COSTS DCOSTS,D.FREQUENCY,D.DOSAGE_EACH,D.DOSAGE,D.DOSAGE_UNITS,D.ADMINISTRATION,M.USAGE,M.DISCHARGE_TAKING_INDICATOR,T.DEPT_NAME,trunc(D.COSTS/D.QUANTITY,2) DACOSTS FROM DOCT_DRUG_PRESC_DETAIL D, DOCT_DRUG_PRESC_MASTER M,DEPT_DICT T,PATS_IN_HOSPITAL PIH,BED_REC BR WHERE (D.PRESC_NO = M.PRESC_NO) and (D.PRESC_DATE = M.PRESC_DATE)  AND (M.ORDERED_BY=T.DEPT_CODE)    AND (M.PATIENT_ID=PIH.PATIENT_ID AND M.VISIT_ID=PIH.VISIT_ID ) and PIH.WARD_CODE=BR.WARD_CODE AND PIH.BED_NO=BR.BED_NO AND ((D.PRESC_DATE = to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss')) and (D.PRESC_NO = :presc_no)) and D.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取处方发药主记录
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterByPrescDateAndNo(string prescdate, string prescno)
        {
            string sql = "select patient_id,presc_type,prescribed_by from doct_drug_presc_master where presc_date=to_date(:presc_date, 'yyyy-MM-dd HH24:mi:ss') and presc_no=:presc_no and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("presc_date");
            para.Add("presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取医生分组
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private DataTable GetDoctorGroupByName(string name)
        {
            string sql = "select distinct order_group from doctor_group where doctor=:as_perform_name";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_perform_name");
            para_val.Add(name);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="itemclass"></param>
        /// <param name="itemcode"></param>
        /// <param name="itemspec"></param>
        /// <returns></returns>
        private DataTable GetPriceListClassByItem(string itemclass, string itemcode, string itemspec)
        {
            string sql = "SELECT item_name,class_on_inp_rcpt,class_on_reckoning,subj_code,class_on_mr  " +
                " FROM CURRENT_PRICE_LIST  WHERE ( ITEM_CLASS = :as_item_class ) and ( ITEM_CODE = :as_item_code ) " +
                " and ( ITEM_SPEC = :as_item_spec ) " +
                " and ( SYSDATE >= start_date and (SYSDATE < stop_date or stop_date is null))  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and   ROWNUM = 1";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_item_class");
            para.Add("as_item_code");
            para.Add("as_item_spec");
            para_val.Add(itemclass);
            para_val.Add(itemcode);
            para_val.Add(itemspec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];

        }
        /// <summary>
        /// 住院患者费用明细
        /// </summary>
        /// <returns></returns>
        private string InsertInpBillDetail(string as_patient_id, string al_visit_id, string al_item_no, string as_item_class, string ls_item_name, string as_item_code, string as_item_spec, string adec_amount, string as_item_units, string as_order_by, string as_performed_by, string ldec_cur_costs, string ldec_cur_charges, string ldt_date, string as_operator_code, string ls_class_on_reckoning, string as_order_group, string as_order_name, string ls_perform_group, string as_perform_name, string as_order_doctor_code, string adec_price, string ldec_factor, string al_discharge_taking, string ls_class_on_inp_rcpt, string ls_subj_code, string ls_class_on_mr, string as_rcpt_no, string as_oper_type, string as_oper_code, string as_memo, string as_batch_no, string tradeprice,
            string sourceflag, string _DRUG_BATCH_CODE, string _GUID)
        {
            string sql = "insert into inp_bill_detail (" +
                "patient_id,visit_id,item_no,item_class,item_name," +
                "item_code,item_spec,amount,units,ordered_by," +
                "performed_by,costs,charges,billing_date_time,operator_no," +
                "class_on_reckoning,order_group,order_doctor,perform_group,perform_doctor," +
                "doctor_user,item_price,price_quotiety,discharge_taking_indicator,class_on_inp_rcpt," +
                "subj_code,class_on_mr,rcpt_no,oper_type,oper_code," +
                "memo,drug_batch_no,trade_price,source_flag," +
                "  DRUG_BATCH_CODE , GUID,HIS_UNIT_CODE ) values('"
                + as_patient_id + "','" + al_visit_id + "','" + al_item_no + "','" + as_item_class + "','" + ls_item_name + "','"
                + as_item_code + "','" + as_item_spec + "','" + adec_amount + "','" + as_item_units + "','" + as_order_by + "','"
                + as_performed_by + "','" + ldec_cur_costs + "','" + ldec_cur_charges + "',to_date('" + ldt_date + "', 'yyyy-MM-dd HH24:mi:ss'),'" + as_operator_code + "','"
                + ls_class_on_reckoning + "','" + as_order_group + "','" + as_order_name + "','" + ls_perform_group + "','" + as_perform_name + "','"
                + as_order_doctor_code + "','" + adec_price + "','" + ldec_factor + "','" + al_discharge_taking + "','" + ls_class_on_inp_rcpt + "','"
                + ls_subj_code + "','" + ls_class_on_mr + "','" + as_rcpt_no + "','" + as_oper_type + "','" + as_oper_code + "','"
                + as_memo + "','" + as_batch_no + "','" + tradeprice + "','" + sourceflag + "','"
                + _DRUG_BATCH_CODE + "','" + _GUID + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 根据煎药代码去价钱
        /// </summary>
        /// <param name="decoctcode"></param>
        /// <returns></returns>
        private DataTable GetPriceListByDecocotCode(string decoctcode)
        {
            string sql = "select price,item_name,item_spec,units,item_class from CURRENT_PRICE_LIST where item_code=:ls_drug_code  and (start_date<=sysdate and (stop_date>sysdate or stop_date is null)) and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_drug_code");
            para_val.Add(decoctcode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取住院收费明细inp_bill_detail的最大项目序号
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetMaxItemNoByPatientIDAndVisitID(string patientid, string visitid)
        {
            string sql = "select nvl(max(item_no),0) FROM inp_bill_detail WHERE patient_id = :ls_patient_id AND visit_id = :ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 根据医生变化获取科室编号
        /// </summary>
        /// <param name="doctoruser"></param>
        /// <returns></returns>
        private DataTable GetDeptCodeByUserName(string doctoruser)
        {
            string sql = "select t.dept_code from staff_dict t where t.user_name=:ls_doctor_user  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_doctor_user");
            para_val.Add(doctoruser);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取审核人审核时间
        /// </summary>
        /// <param name="prescno"></param>
        /// <param name="prescdate"></param>
        /// <returns></returns>
        private DataTable GetPrescMasterByNoAndDate(string prescno, string prescdate)
        {
            string sql = "select  verified_datetime, verify_by from doct_drug_presc_master where presc_no = :ll_presc_no and presc_date =to_date(:ldt_presc_date,'yyyy-mm-dd hh24:mi:ss')  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ll_presc_no");
            para.Add("ldt_presc_date");
            para_val.Add(prescno);
            para_val.Add(prescdate);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 开单科室不从医生对应的科室取，改为病人所在的科室 
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPatVisitDeptCode(string patientid, string visitid)
        {
            string sql = "select dept_discharge_from from pat_visit where  patient_id=:ls_patient_id and visit_id=:ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 开单科室不从医生对应的科室取，改为病人所在的科室 
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPatInHospDeptCode(string patientid, string visitid)
        {
            string sql = "select dept_code from pats_in_hospital where  patient_id=:ls_patient_id and visit_id=:ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 找不到用 经治医生
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetOrderGroupRecByPatientID(string patientid, string visitid)
        {
            string sql = "select doctor_user,order_doctor,order_group from orders_group_rec	where patient_id = :ls_patient_id and visit_id = :ll_visit_id ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 取开单医生代码,开单医生姓名 ,开单医生组
        /// </summary>
        /// <param name="doctoruser"></param>
        /// <returns></returns>
        private DataTable GetDoctorGroupByDoctorUser(string doctoruser)
        {
            string sql = "select doctor,order_group from doctor_group where doctor_user = :ls_doctor_user ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_doctor_user");
            para_val.Add(doctoruser);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 返回库存
        /// </summary>
        /// <returns></returns>
        private DataTable GetDrugStockForUpdate(string storagecode, string drugcode, string drugspec, string packagespec, string firmid)
        {
            string sql = "SELECT S.DRUG_CODE,S.DRUG_NAME,S.TRADE_NAME,D.DOSE_PER_UNIT,D.DOSE_UNITS," +
                "0.0000000000 orders_dosage_in_stock_units,S.DRUG_SPEC,S.UNITS,S.FIRM_ID,S.PACKAGE_SPEC," +
                "S.PACKAGE_UNITS,S.BATCH_NO,S.QUANTITY,S.BATCH_CODE,S.EXPIRE_DATE,S.PURCHASE_PRICE," +
                "S.DISCOUNT,S.TRADE_PRICE,S.RETAIL_PRICE,S.SUPPLIER,S.SUB_STORAGE,S.LOCATION_CODE," +
                "(select max(amount_per_package) " +
                "from CURRENT_DRUG_MD_PRICE_LIST temp where temp.drug_code =S.drug_code and temp.min_spec=S.drug_spec " +
                "and temp.drug_spec=S.package_spec and temp.firm_id = S.firm_id  and temp.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and temp.START_DATE <= sysdate " +
                "and (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null ) ) amount_per_package," +
                "S.STORAGE,S.PACKAGE_1,S.PACKAGE_SPEC_1,S.PACKAGE_UNITS_1,S.PACKAGE_2,S.PACKAGE_SPEC_2," +
                "S.PACKAGE_UNITS_2,S.SUPPLY_INDICATOR,S.DOCUMENT_NO,S.PURCHASE_PRICE_LAST,S.FROZEN_FLAG," +
                "S.QUANTITY_PRE,S.LAST_UPDATETIME , S.GUID " +
                "FROM DRUG_STOCK S,DRUG_DICT D " +
                "WHERE ( S.DRUG_CODE = D.DRUG_CODE ) and  ( D.DRUG_SPEC = S.DRUG_SPEC ) " +
                "and  ( ( S.STORAGE = :as_storage_code ) AND  ( S.SUPPLY_INDICATOR = 1 ) )  " +
                "AND ( S.QUANTITY > 0 ) and S.drug_code =:as_drug_code " +
                " and S.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'" +
                "and S.drug_spec =:as_drug_spec and S.package_spec =:as_package_spec and S.firm_id =:as_firm_id " +
                "ORDER BY S.EXPIRE_DATE ASC, S.BATCH_NO ASC";  // 2025-07-20 添加：优先使用快过期的批次
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_storage_code");
            para.Add("as_drug_code");
            para.Add("as_drug_spec");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(packagespec);
            para_val.Add(firmid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 修改住院处方主表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string UpdateDoctPrescMasterByPrescDateAndNo(string username, string storagecode, string prescdate, string prescno)
        {
            string sql = "UPDATE DOCT_DRUG_PRESC_MASTER SET DISPENSING_PROVIDER='" + username + "',PRESC_STATUS='1',DISPENSARY='" + storagecode + "' WHERE PRESC_DATE = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND PRESC_NO='" + prescno + "'and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            return sql;
        }
        /// <summary>
        /// 添加处方明细表
        /// </summary>
        /// <param name="ddr"></param>
        /// <returns></returns>
        private string InsertDoctDrugPrescDetail(DataRow ddr)
        {
            string sql = "Insert into DRUG_PRESC_DETAIL( " +
                " PRESC_DATE,PRESC_NO,ITEM_NO,DRUG_CODE,DRUG_NAME," +
                "TRADE_NAME,DRUG_SPEC,UNITS,PACKAGE_SPEC,PACKAGE_UNITS," +
                "FIRM_ID,BATCH_NO,GEN_SERIAL,QUANTITY,INVENTORY," +
                "BATCH_CODE,EXPIRE_DATE,PURCHASE_PRICE,TRADE_PRICE,RETAIL_PRICE," +
                "SUPPLIER,COSTS,PAYMENTS,ROUND_AMT,ORDER_NO," +
                "ORDER_SUB_NO,ADMINISTRATION,DOSAGE_EACH,DOSAGE_UNITS,FREQUENCY," +
                "FREQ_DETAIL,HANDBACK_AMOUNT,INSUR_ADULT , GUID,HIS_UNIT_CODE ) values (" +
                "to_date('" + ddr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'" + ddr["PRESC_NO"] + "','" + ddr["ITEM_NO"] + "','" + ddr["DRUG_CODE"] + "','" + ddr["DRUG_NAME"] + "','"
                + ddr["TRADE_NAME"] + "','" + ddr["DRUG_SPEC"] + "','" + ddr["UNITS"] + "','" + ddr["PACKAGE_SPEC"] + "','" + ddr["PACKAGE_UNITS"] + "','"
                + ddr["FIRM_ID"] + "','" + ddr["BATCH_NO"] + "','" + ddr["GEN_SERIAL"] + "','" + ddr["QUANTITY"] + "','" + ddr["INVENTORY"] + "','"
                + ddr["BATCH_CODE"] + "',to_date('" + ddr["EXPIRE_DATE"] + "','yyyy-MM-dd'),'" + ddr["PURCHASE_PRICE"] + "','" + ddr["TRADE_PRICE"] + "','" + ddr["RETAIL_PRICE"] + "','"
                + ddr["SUPPLIER"] + "','" + ddr["COSTS"] + "','" + ddr["PAYMENTS"] + "','" + ddr["ROUND_AMT"] + "','" + ddr["ORDER_NO"] + "','"
                + ddr["ORDER_SUB_NO"] + "','" + ddr["ADMINISTRATION"] + "','" + ddr["DOSAGE_EACH"] + "','" + ddr["DOSAGE_UNITS"] + "','" + ddr["FREQUENCY"] + "','"
                + ddr["FREQ_DETAIL"].ToString().Replace("'", "") + "','" + ddr["HANDBACK_AMOUNT"] + "','" + ddr["INSUR_ADULT"] + "','" + ddr["GUID"] + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 生成序号
        /// </summary>
        /// <returns></returns>
        private string GetGenSerialSeq()
        {
            //string sql = "select GEN_SERIAL_SEQ.nextval from dual";
            //DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            //if (dt.Rows.Count < 1)
            //{
            //    return "";
            //}
            //else
            //{
            //    return dt.Rows[0][0] == DBNull.Value ? "" : dt.Rows[0][0].ToString();
            //}
            //改成从配置表取 by lions 2018-06-27
            #region
            string sysdate = PlatCommon.Common.PublicFunction.GetSysDate();
            string ls_gen_serail = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("药品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_gen_serail) || string.IsNullOrEmpty(ls_gen_serail) || string.IsNullOrEmpty(sysdate))
            {
                return "";//获取序列失败
            }
            ls_gen_serail = DateTime.Parse(sysdate).ToString("yyMMdd") + ls_gen_serail;
            return ls_gen_serail;
            #endregion
        }
        /// <summary>
        ///修改药品库存 - 2025-01-17 修复：添加防止负库存的安全检查
        /// </summary>
        /// <returns></returns>
        private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode, string drugspec, string packagespec, string firmid, string batchno)
        {
            // 2025-01-17 修复：确保扣减后库存不为负数
            // 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
            // 修复后：QUANTITY >= quantity 确保库存充足才能扣减
            // 同时修复：quantity参数不应该用引号包围（数值类型）
            string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "' and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' and firm_id = '" + firmid + "' and BATCH_NO='" + batchno + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            return sql;

        }
        /// <summary>
        /// 添加处方主表
        /// </summary>
        /// <param name="mdr"></param>
        /// <returns></returns>
        private string InsertDoctDrugPrescMaster(DataRow mdr, string batchno, string storagecode)
        {
            string sql = @"INSERT INTO DRUG_PRESC_MASTER(PRESC_DATE,PRESC_NO,DISPENSARY,PATIENT_ID,VISIT_ID,CLINIC_NO,NAME,NAME_PHONETIC,AGE,IDENTITY,CHARGE_TYPE,UNIT_IN_CONTRACT,DIAG_DESC,PRESC_TYPE,PRESC_ATTR,PRESC_SOURCE,DISCHARGE_TAKING_INDICATOR,DECOCTION,REPETITION,COUNT_PER_REPETITION,ORDERED_BY,DOCTOR_USER,PRESCRIBED_USERCODE,PRESCRIBED_BY,ENTERED_USERCODE,ENTERED_BY,ENTERED_DATETIME,VERIFY_USERCODE,VERIFY_BY,VERIFIED_DATETIME,RCPT_NO,COSTS,PAYMENTS,ROUND_AMT,DISPENSING_USERCODE,DISPENSING_PROVIDER,DISPENSING_DATETIME,FLAG,ORIGINAL_PRESC_DATE,ORIGINAL_PRESC_NO,RETURN_VISIT_DATE,RETURN_VISIT_NO,BATCH_PROVIDE_NO,sex,BED_GROUP,HIS_UNIT_CODE ) VALUES (to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'" + mdr["PRESC_NO"] + "','" + mdr["DISPENSARY"] + "','" + mdr["PATIENT_ID"] + "' ,'" + mdr["VISIT_ID"] + "' ,'' ,'" + mdr["NAME"] + "' ,'" + mdr["NAME_PHONETIC"] + "' ,'" + mdr["AGE"] + "' ,'" + mdr["IDENTITY"] + "' ,'" + mdr["CHARGE_TYPE"] + "' ,'" + mdr["UNIT_IN_CONTRACT"] + "' ,'" + mdr["DIAGNOSIS_NAME"] + "' ,'" + mdr["PRESC_TYPE"] + "' ,'" + mdr["PRESC_ATTR"] + "' ,'" + mdr["PRESC_SOURCE"] + "' ,'" + mdr["DISCHARGE_TAKING_INDICATOR"] + "' ,'" + mdr["DECOCTION"] + "' ,'" + mdr["REPETITION"] + "' ,'" + mdr["COUNT_PER_REPETITION"] + "' ,'" + mdr["ORDERED_BY"] + "' ,'" + mdr["DOCTOR_USER"] + "' ,'" + mdr["PRESCRIBED_USERCODE"] + "' ,'" + mdr["PRESCRIBED_BY"] + "' ,'" + mdr["ENTERED_USERCODE"] + "' ,'" + mdr["ENTERED_BY"] + "' ,to_date('" + mdr["ENTERED_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss') ,'" + mdr["VERIFY_USERCODE"] + "' ,'" + mdr["VERIFY_BY"] + "' ,to_date('" + mdr["VERIFIED_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss') ,'" + mdr["RCPT_NO"] + "' ,'" + mdr["COSTS"] + "' ,'" + mdr["PAYMENTS"] + "' ,'" + mdr["ROUND_AMT"] + "' ,'" + mdr["DISPENSING_USERCODE"] + "' ,'" + mdr["DISPENSING_PROVIDER"] + "' ,to_date('" + DISPENSING_DATETIME_PRINT + "','yyyy-MM-dd HH24:mi:ss') ,'1' ,to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss') ,'" + mdr["PRESC_NO"] + "' ,to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss') ,'" + mdr["PRESC_NO"] + "','" + batchno + "','" + mdr["sex"] + "','" + masterdr["BED_GROUP"] + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }

        /// <summary>
        /// 返回预交金额
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetPrepaymentByPatientID(string patientid, int visitid)
        {
            string sql = @"
                SELECT SUM(AMOUNT)
                  FROM PREPAYMENT_RCPT
                 WHERE PAY_WAY = '现金'
                   AND PATIENT_ID = :as_patient_id
                   AND VISIT_ID = :ai_visit_id
                   AND TRANSACT_TYPE <> '作废' ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_patient_id");
            para.Add("ai_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 判断病人是否出院或者已办理结帐
        /// </summary>
        /// <param name="patientid"></param>
        /// <returns></returns>
        private DataTable GetPatInHospitapByPatientID(string patientid)
        {
            string sql = @"
                SELECT settled_indicator
                  FROM pats_in_hospital
                 Where patient_id = :ls_patient_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para_val.Add(patientid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 判断病人是不是出院全结
        /// </summary>
        /// <param name="patientid"></param>
        /// <param name="visitid"></param>
        /// <returns></returns>
        private DataTable GetInpSettleMasterByPatientID(string patientid, int visitid)
        {
            string sql = @"
                SELECT count(*)
                  From inp_settle_master
                 Where transact_type = '正常'
                   And settle_type_name = '出院全结'
                   And patient_id = :ls_patient_id
                   And visit_id = :ll_visit_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para.Add("ll_visit_id");
            para_val.Add(patientid);
            para_val.Add(visitid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取批量发药单据号
        /// </summary>
        /// <returns></returns>
        private DataTable GetBatchProvideNo()
        {
            string sql = @"
                SELECT to_char(sysdate, 'yyyymmdd') || (lpad(pharmacy.batch_provide_no_seq.nextval, 6, '0'))
                  From dual ";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }

        /// <summary>
        /// 获取系统时间
        /// </summary>
        /// <returns></returns>
        private DateTime GetSystemTime()
        {
            DateTime dt = new ServerPublicClient().GetSysDate();
            return dt;
        }

        /// <summary>
        /// 获取药品库存
        /// </summary>
        /// <param name="storagecode"></param>
        /// <param name="drugcode"></param>
        /// <param name="firmid"></param>
        /// <param name="packagespec"></param>
        /// <returns></returns>
        private DataTable GetDrugStockByDrugCode(string storagecode, string drugcode, string firmid, string packagespec)
        {
            string sql = @"
                select sum(drug_stock.quantity)
                  FROM drug_stock
                 where drug_stock.storage = :gs_storage_code
                   and drug_stock.drug_code = :ls_drug_code
                   and drug_stock.firm_id = :ls_firm_id
                   and drug_stock.PACKAGE_SPEC = :ls_package_spec and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("gs_storage_code");
            para.Add("ls_drug_code");
            para.Add("ls_firm_id");
            para.Add("ls_package_spec");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(firmid);
            para_val.Add(packagespec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取药品价格
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="drugspec"></param>
        /// <param name="firmid"></param>
        /// <returns></returns>
        private DataTable GetDrugPriceDrugCode(string drugcode, string drugspec, string firmid)
        {
            string sql = @"
                select TRADE_PRICE, retail_price
                  from CURRENT_DRUG_MD_PRICE_LIST
                 where drug_code = :as_DRUG_CODE
                   and drug_spec = :as_package_spec
                   and firm_id = :as_firm_id
                   and (start_date < sysdate and
                       (stop_date >= sysdate or stop_date is null))  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_DRUG_CODE");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(firmid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 判断是否是新生儿
        /// </summary>
        /// <returns></returns>
        private DataTable CheckNewBorn(string patiendid)
        {
            string sql = @"
                select patient_id_of_mother, visit_id_of_mother
                  from newborn_rec
                 where patient_id = :ls_patient_id and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_patient_id");
            para_val.Add(patiendid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取收费系数字典
        /// </summary>
        /// <param name="chargetype"></param>
        /// <param name="hisunitcode"></param>
        /// <returns></returns>
        private DataTable GetChargePriceSchedule(string chargetype, string hisunitcode)
        {
            string sql = @"
                Select price_coeff_numerator,
                       price_coeff_denominator,
                       charge_special_indicator
                  From charge_price_schedule
                 Where charge_type = :chargetype
                   and his_unit_code = :hisunitcode ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("chargetype");
            para.Add("hisunitcode");
            para_val.Add(chargetype);
            para_val.Add(hisunitcode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取药品价格
        /// </summary>
        /// <param name="itemclass"></param>
        /// <param name="itemcode"></param>
        /// <param name="itemspec"></param>
        /// <returns></returns>
        private DataTable GetPriceListByClassCodeSpec(string itemclass, string itemcode, string itemspec)
        {
            string sql = @"
                SELECT p.price
                  FROM CURRENT_PRICE_LIST p
                 WHERE p.item_class = :ls_item_class
                   AND p.item_code = :ls_item_code
                   AND p.item_spec = :ls_item_spec
                   AND (sysdate >= p.start_date)
                   AND (sysdate <= p.stop_date Or p.stop_date Is null)  and p.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_item_class");
            para.Add("ls_item_code");
            para.Add("ls_item_spec");
            para_val.Add(itemclass);
            para_val.Add(itemcode);
            para_val.Add(itemspec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 根据处方号和时间获取住院处方明细信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDoctPrescDetailTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = @"
                SELECT D.PRESC_DATE,
                       D.PRESC_NO,
                       D.ITEM_NO,
                       D.DRUG_CODE,
                       D.DRUG_NAME,
                       D.TRADE_NAME,
                       D.DRUG_SPEC,
                       D.UNITS,
                       D.PACKAGE_SPEC,
                       D.PACKAGE_UNITS,
                       D.FIRM_ID,
                       D.QUANTITY,
                       D.BATCH_NO,
                       D.BATCH_CODE,
                       D.EXPIRE_DATE,
                       D.PURCHASE_PRICE,
                       D.TRADE_PRICE,
                       D.RETAIL_PRICE,
                       D.SUPPLIER,
                       D.COSTS,
                       D.PAYMENTS,
                       D.ROUND_AMT,
                       D.ORDER_NO,
                       D.ORDER_SUB_NO,
                       D.ADMINISTRATION,
                       D.DOSAGE,
                       D.DOSAGE_EACH,
                       D.DOSAGE_UNITS,
                       D.FREQUENCY,
                       D.FREQ_DETAIL,
                       D.AMOUNT_PER_PACKAGE,
                       D.MEMO,
                       D.WRITEOFF,
                       '' ERR_MSG,
                       (D.ADMINISTRATION || D.FREQUENCY) ADMINISTRATIONFREQUENCY,
                       D.INSUR_ADULT  , D.GUID
                  FROM DOCT_DRUG_PRESC_DETAIL D
                 WHERE D.PRESC_DATE = :adt_presc_date
                   AND D.PRESC_NO = :al_presc_no  and D.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_presc_date");
            para.Add("al_presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取药品库存量，仓库，货位，批次
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="firmid"></param>
        /// <param name="packagespec"></param>
        /// <param name="packageunit"></param>
        /// <param name="storagecode"></param>
        /// <returns></returns>
        private DataTable GetDrugQuantityAndStorageAndLocation(string drugcode, string firmid, string packagespec, string packageunit, string storagecode)
        {
            string sql =$@"
                SELECT QUANTITY, SUPPLY_INDICATOR, SUB_STORAGE, LOCATION_CODE, BATCH_NO
                  FROM DRUG_STOCK
                 WHERE DRUG_CODE = :drug_code1
                   AND FIRM_ID = :firm_id1
                   AND PACKAGE_SPEC = :package_spec1
                   AND PACKAGE_UNITS = :package_units1
                   AND STORAGE = :gs_storage_code
                   and HIS_UNIT_CODE='{SystemParm.HisUnitCode}'
                 order by batch_no ASC ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("drug_code1");
            para.Add("firm_id1");
            para.Add("package_spec1");
            para.Add("package_units1");
            para.Add("gs_storage_code");
            para_val.Add(drugcode);
            para_val.Add(firmid);
            para_val.Add(packagespec);
            para_val.Add(packageunit);
            para_val.Add(storagecode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 根据病人ID获取生日
        /// </summary>
        /// <param name="patientid"></param>
        /// <returns></returns>
        private DataTable GetBirthDayByPatientID(string patientid)
        {
            string sql = @"
                select sex, date_of_birth
                  from pat_master_index
                 where patient_id = :fs_Patient_Id  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("fs_Patient_Id");
            para_val.Add(patientid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 根据处方号和时间获取住院处方主信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetDoctPrescMasterTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = $@"
                SELECT M.PRESC_DATE,
                       M.PRESC_NO,
                       M.DISPENSARY,
                       M.PATIENT_ID,
                       M.VISIT_ID,
                       M.NAME,
                       M.NAME_PHONETIC,
                       M.SEX,
                       M.AGE,
                       M.IDENTITY,
                       M.CHARGE_TYPE,
                       M.UNIT_IN_CONTRACT,
                       M.DIAGNOSIS_NAME,
                       M.PRESC_TYPE,
                       M.PRESC_ATTR,
                       M.PRESC_SOURCE,
                       M.DISCHARGE_TAKING_INDICATOR,
                       M.DECOCTION,
                       M.REPETITION,
                       M.COUNT_PER_REPETITION,
                       M.ORDERED_BY,
                       M.PRESCRIBED_USERCODE,
                       M.PRESCRIBED_BY,
                       M.ENTERED_USERCODE,
                       M.ENTERED_BY,
                       M.ENTERED_DATETIME,
                       M.VERIFY_USERCODE,
                       M.VERIFY_BY,
                       M.VERIFIED_DATETIME,
                       M.COSTS,
                       M.PAYMENTS,
                       M.ROUND_AMT,
                       M.DISPENSING_USERCODE,
                       M.DISPENSING_PROVIDER,
                       M.DISPENSING_DATETIME,
                       M.BINDING_PRESC_TITLE,
                       M.USAGE,
                       M.PRESC_STATUS,
                       M.STATUS,
                       M.DOCTOR_USER,
                       0 prepayment,
                       '' rcpt_no,
                       '' bed_group
                  FROM DOCT_DRUG_PRESC_MASTER M
                 WHERE M.PRESC_DATE = :adt_presc_date
                   AND M.PRESC_NO = :al_presc_no  and M.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_presc_date");
            para.Add("al_presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 获取全部科室
        /// </summary>
        /// <returns></returns>
        private DataTable GetAllDept()
        {
            string sql = @"
                select dept_code, dept_name, INPUT_CODE
                  from dept_dict
                 where outp_or_inp = '1'
                   and clinic_attr = '2'
                   and his_unit_code='"+SystemParm.HisUnitCode+@"'
                 order by dept_code ";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return dt;
        }

        /// <summary>
        /// 获取处方病人主信息
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <returns></returns>
        private DataTable GetDoctDrugPrescMasterByTimeAndDept(DateTime begintime, DateTime endtime, string storagecode, string orderedby, string presAtrr)
        {
            //string sql = @"
            //    SELECT M.PRESC_NO,
            //           M.NAME,
            //           M.Presc_Attr,
            //           M.NAME_PHONETIC,
            //           M.PRESC_DATE,
            //           M.PAYMENTS,
            //           M.PATIENT_ID,
            //           M.ORDERED_BY,
            //           NVL(M.DISCHARGE_TAKING_INDICATOR, 0) DISCHARGE_TAKING_INDICATOR,
            //           '1' as AS_COMFIRM,
            //           M.PRESC_TYPE,
            //           T.DEPT_NAME,
            //           (case M.PRESC_TYPE when 0 then '西药' else '中药' end) PRESC_TYPE_NAME
            //      FROM DOCT_DRUG_PRESC_MASTER M,
            //           DEPT_DICT              D,
            //           PATS_IN_HOSPITAL       P,
            //           DEPT_DICT              T,
            //           bed_rec                b
            //     WHERE M.PATIENT_ID = P.PATIENT_ID(+)
            //       and M.VISIT_ID = P.VISIT_ID(+)
            //       and p.bed_no = b.bed_no
            //       and p.ward_code = b.ward_code
            //       and M.ORDERED_BY = T.DEPT_CODE
            //       and M.PRESC_DATE >= :adt_begin
            //       and M.PRESC_DATE <= :adt_end
            //       and M.DISPENSARY = :as_storage_code
            //       and M.PRESC_STATUS = 0
            //       and ((case P.LEND_INDICATOR when 1 then P.DEPT_CODE else M.ORDERED_BY end) = D.DEPT_CODE)
            //       and P.WARD_CODE = :as_ordered_by ";
            //if (!string.IsNullOrEmpty(bedGroup))
            //{
            //    sql += " and bed_group like '" + bedGroup + "%' ";
            //}

            string sql = $@"select * from(
SELECT '1' as AS_COMFIRM,DOCT_DRUG_PRESC_MASTER.PRESC_NO,
       DOCT_DRUG_PRESC_MASTER.NAME,
       DOCT_DRUG_PRESC_MASTER.NAME_PHONETIC,
       DOCT_DRUG_PRESC_MASTER.PRESC_DATE,
       DOCT_DRUG_PRESC_MASTER.PAYMENTS,
       DOCT_DRUG_PRESC_MASTER.PATIENT_ID,
       DOCT_DRUG_PRESC_MASTER.ORDERED_BY,
       DOCT_DRUG_PRESC_MASTER.DISCHARGE_TAKING_INDICATOR,
       case when DOCT_DRUG_PRESC_MASTER.DISCHARGE_TAKING_INDICATOR=0 then '是'
         else '' end as TAKING_INDICATOR,
        BED_NO,       
        case when DOCT_DRUG_PRESC_MASTER.PRESC_TYPE=0 then '西药'
         else '中药' end as PRESC_TYPE_NAME,
       DOCT_DRUG_PRESC_MASTER.PRESC_TYPE,
       (select b.bed_label
          from bed_rec b
         where PATS_IN_HOSPITAL.ward_code = b.ward_code
           and PATS_IN_HOSPITAL.bed_no = b.bed_no) ch,
       (select b.toxi_property
          from DOCT_DRUG_PRESC_detail a, drug_dict b
         where a.drug_code = b.drug_code
           and a.Presc_date = DOCT_DRUG_PRESC_MASTER.presc_date
           and a.presc_no = DOCT_DRUG_PRESC_MASTER.presc_no
           and a.HIS_UNIT_CODE='{SystemParm.HisUnitCode }'
           and rownum = 1) presc_attr,
           dept_dict.dept_name
  FROM DOCT_DRUG_PRESC_MASTER, DEPT_DICT, PATS_IN_HOSPITAL
 WHERE (DOCT_DRUG_PRESC_MASTER.PATIENT_ID =
       PATS_IN_HOSPITAL.PATIENT_ID(+))
   and (DOCT_DRUG_PRESC_MASTER.VISIT_ID =
       PATS_IN_HOSPITAL.VISIT_ID(+))";
            sql += "   and DOCT_DRUG_PRESC_MASTER.HIS_UNIT_CODE='"+SystemParm.HisUnitCode+"'";
            sql += " and (DOCT_DRUG_PRESC_MASTER.PRESC_DATE >=";
            sql += "    to_date('" + begintime + "', ' yyyy - mm - dd hh24 :mi :ss '))";
            sql += "and (DOCT_DRUG_PRESC_MASTER.PRESC_DATE <=";
            sql += "   to_date('" + endtime + "', ' yyyy - mm - dd hh24 :mi :ss '))";
            sql += "AND (DOCT_DRUG_PRESC_MASTER.DISPENSARY = '" + storagecode + "')";
            sql += " AND DOCT_DRUG_PRESC_MASTER.PRESC_STATUS = 0";
            sql += " AND ((case PATS_IN_HOSPITAL.LEND_INDICATOR";
            sql += "      when 1 then";
            sql += "      PATS_IN_HOSPITAL.DEPT_CODE";
            sql += "     else";
            sql += "     DOCT_DRUG_PRESC_MASTER.ORDERED_BY";
            sql += "   end) = DEPT_DICT.DEPT_CODE)";
            sql += "   and DOCT_DRUG_PRESC_MASTER.ORDERED_BY = '" + orderedby + "')a";
            if (!string.IsNullOrEmpty(presAtrr) && presAtrr != "全部")
            {
                sql += "  where presc_attr='" + presAtrr + "'";
            }

            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //List<string> para = new List<string>();
            //ArrayList para_val = new ArrayList();
            //para.Add("adt_begin");
            //para.Add("adt_end");
            //para.Add("as_storage_code");
            //para.Add("as_ordered_by");
            //if (!string.IsNullOrEmpty(presAtrr))
            //{
            //    para.Add("presc_attr");
            //}
            //para_val.Add(begintime);
            //para_val.Add(endtime);
            //para_val.Add(storagecode);
            //para_val.Add(orderedby);
            //if (!string.IsNullOrEmpty(presAtrr))
            //{
            //    para_val.Add(presAtrr);
            //}
            DataSet ds = new DataSet();
            ds = spc.GetDataBySql(sql);
            if (ds != null)
                return ds.Tables[0];
            else
                return null;
        }
        #endregion

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //gv1.CloseEditor();
            //gv1.UpdateCurrentRow();
            //DataTable batchnodt = GetBatchProvideNo();
            //string batchno = "";
            //if (batchnodt.Rows.Count > 0)
            //{
            //    batchno = batchnodt.Rows[0][0].ToString();
            //}
            //else
            //{
            //    batchno = GetSystemTime().ToString("yyyyMMdd") + "000001";
            //}

            //DataTable masterlist = (DataTable)gc1.DataSource;
            //if (masterlist == null) return;
            //DataRow[] selectmaster = masterlist.Select("AS_COMFIRM='1'");//获取处方主列表选中行
            //if (selectmaster.Length == 0)
            //{
            //    //XtraMessageBox.Show("请选择要批量发药的处方!", "提示");
            //    return;
            //}
            //FrmPass4 fp4 = new FrmPass4(this.AppCode, this.DeptCode);
            //fp4.drc = selectmaster;
            //fp4.ordered_by = txtDeptName.Text;
            //fp4.ShowDialog();
            //bbtnrefresh_ItemClick(null, null);
        }

        private void Getdt_printClone()
        {
            dt_print.Clear();
            dt_print.Columns.Add("PRESC_DATE");
            dt_print.Columns.Add("PRESC_NO");
            dt_print.Columns.Add("AGE");
            dt_print.Columns.Add("SEX");
            dt_print.Columns.Add("DISPENSARY");
            dt_print.Columns.Add("DISPENSARY_NAME");
            dt_print.Columns.Add("PATIENT_ID");
            dt_print.Columns.Add("NAME");
            dt_print.Columns.Add("BED_LABEL");
            dt_print.Columns.Add("IDENTITY");
            dt_print.Columns.Add("CHARGE_TYPE");
            dt_print.Columns.Add("UNIT_IN_CONTRACT");
            dt_print.Columns.Add("PRESC_SOURCE");
            dt_print.Columns.Add("REPETITION");
            dt_print.Columns.Add("ORDERED_BY");
            dt_print.Columns.Add("PRESCRIBED_BY");
            dt_print.Columns.Add("VISIT_ID");
            dt_print.Columns.Add("ITEM_NO");
            dt_print.Columns.Add("DRUG_NAME");
            dt_print.Columns.Add("FIRM_ID");
            dt_print.Columns.Add("PACKAGE_SPEC");
            dt_print.Columns.Add("PACKAGE_UNITS");
            dt_print.Columns.Add("QUANTITY");
            dt_print.Columns.Add("NAME_PHONETIC");
            dt_print.Columns.Add("PRESC_TYPE");
            dt_print.Columns.Add("COSTS");
            dt_print.Columns.Add("PAYMENTS");
            dt_print.Columns.Add("ENTERED_BY");
            dt_print.Columns.Add("COUNT_PER_REPETITION");
            dt_print.Columns.Add("DCOSTS");
            dt_print.Columns.Add("FREQUENCY");
            dt_print.Columns.Add("DOSAGE_EACH");
            dt_print.Columns.Add("DOSAGE");
            dt_print.Columns.Add("DOSAGE_UNITS");
            dt_print.Columns.Add("ADMINISTRATION");
            dt_print.Columns.Add("USAGE");
            dt_print.Columns.Add("DISCHARGE_TAKING_INDICATOR");
            dt_print.Columns.Add("DEPT_NAME");
            dt_print.Columns.Add("DACOSTS");
        }

        private void lookUpEditGroup_EditValueChanged(object sender, EventArgs e)
        {
            SetPrescMaster();
        }

        /// <summary>
        /// 全选/全不选
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barEditItem1_EditValueChanged(object sender, EventArgs e)
        {
            DataTable masterlist = (DataTable)gc1.DataSource;
            if (masterlist == null) return;

            foreach (DataRow dr in masterlist.Rows)
            {
                dr["AS_COMFIRM"] = barEditItem1.EditValue.Equals(true) ? "1" : "0";
            }
        }

        private void groupControl2_Paint(object sender, PaintEventArgs e)
        {

        }

        private void gv1_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            GridView View = sender as GridView;
            if (e.RowHandle >= 0)
            {
                string needAlert = View.GetRowCellValue(e.RowHandle, View.Columns["PRESC_ATTR"]).ToString();
                if (needAlert == "精神二类" || needAlert == "精神一类" || needAlert == "麻醉药品")
                {
                    e.Appearance.BackColor = Color.Orange;
                }
                //string needAlert = View.GetRowCellValue(e.RowHandle, View.Columns["PRESC_ATTR"]).ToString();
                //if (needAlert.Contains("精二")||needAlert.Contains("精一")||needAlert.Contains("麻醉"))
                //{
                //    e.Appearance.BackColor = Color.Orange;
                //}
            }
        }


        private void barPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            RepertPrint(prescdateprint, prescnoprint, true);
        }
    }
}
