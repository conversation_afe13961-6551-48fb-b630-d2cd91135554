using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Text;
using PlatCommon.SysBase;

namespace Tjhis.Presdisp.Station.Common
{
    /// <summary>
    /// 统一库存验证器 - 与门诊医生站保持一致的库存计算逻辑
    /// 创建日期：2025-07-20
    /// 目的：解决开药与发药库存逻辑不一致的问题
    /// </summary>
    public static class UnifiedStockValidator
    {
        /// <summary>
        /// 统一的药品库存验证方法（与门诊医生站逻辑完全一致）
        /// </summary>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugName">药品名称</param>
        /// <param name="drugSpec">药品规格</param>
        /// <param name="firmId">厂家ID</param>
        /// <param name="dispensary">药房代码</param>
        /// <param name="requiredQuantity">需要数量</param>
        /// <param name="units">单位</param>
        /// <returns>验证结果和详细信息</returns>
        public static StockValidationResult ValidateStock(string drugCode, string drugName, string drugSpec,
            string firmId, string dispensary, decimal requiredQuantity, string units)
        {
            // 【紧急调试】强制写入到当前目录，确保能看到日志
            try
            {
                string emergencyLog = $"紧急调试_{DateTime.Now:yyyyMMdd}.log";
                string emergencyEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [紧急调试] ValidateStock被调用 - 药品：{drugName}({drugCode})，数量：{requiredQuantity}{units}{Environment.NewLine}";
                System.IO.File.AppendAllText(emergencyLog, emergencyEntry, Encoding.UTF8);
            }
            catch { }

            // 【增强调试】记录所有验证请求，包括参数信息
            WriteDebugLog($"[ValidateStock] ========== 库存验证请求 ==========");
            WriteDebugLog($"[ValidateStock] 药品代码：{drugCode}");
            WriteDebugLog($"[ValidateStock] 药品名称：{drugName}");
            WriteDebugLog($"[ValidateStock] 药品规格：{drugSpec}");
            WriteDebugLog($"[ValidateStock] 厂家ID：{firmId}");
            WriteDebugLog($"[ValidateStock] 药房：{dispensary}");
            WriteDebugLog($"[ValidateStock] 需要数量：{requiredQuantity}{units}");
            WriteDebugLog($"[ValidateStock] 当前用户：{SystemParm.LoginUser?.EMP_NO ?? "未知"}");
            WriteDebugLog($"[ValidateStock] 医院代码：{SystemParm.HisUnitCode ?? "未知"}");

            try
            {
                // 检查是否启用统一库存验证
                WriteDebugLog($"[ValidateStock] 正在检查统一库存验证参数...");
                WriteDebugLog($"[ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE={dispensary}, EMP_NO={SystemParm.LoginUser.EMP_NO}, HIS_UNIT_CODE={SystemParm.HisUnitCode}");
                string enableUnifiedValidation = SystemParm.GetParameterValue("ENABLE_UNIFIED_STOCK_VALIDATION", "PRESDISP", dispensary, SystemParm.LoginUser.EMP_NO, SystemParm.HisUnitCode);
                WriteDebugLog($"[ValidateStock] 参数查询结果：{enableUnifiedValidation ?? "null"}");

                if (enableUnifiedValidation != "1")
                {
                    // 未启用统一验证，返回成功（保持原有逻辑）
                    WriteDebugLog($"[ValidateStock] 统一库存验证未启用 - 药品：{drugName}({drugCode})，参数值：'{enableUnifiedValidation}'");
                    WriteDebugLog($"[ValidateStock] 提示：如果参数值为null或空字符串，请检查数据库中的参数配置");
                    WriteDebugLog($"[ValidateStock] 预期参数配置：APP_NAME='PRESDISP', DEPT_CODE='*', EMP_NO='*', PARAMETER_VALUE='1'");
                    WriteDebugLog($"[ValidateStock] ========== 验证结束（未启用）==========");
                    return new StockValidationResult
                    {
                        IsValid = true,
                        ErrorMessage = $"统一库存验证未启用，参数值：'{enableUnifiedValidation}'",
                        TotalStock = 0,
                        AvailableStock = 0,
                        ReservedStock = 0
                    };
                }

                WriteDebugLog($"[ValidateStock] 开始统一库存验证 - 药品：{drugName}({drugCode})，需要数量：{requiredQuantity}{units}");

                // 1. 获取总库存（与门诊医生站完全相同的逻辑）
                WriteDebugLog($"[ValidateStock] 正在查询药品库存 - 药品：{drugCode}，规格：{drugSpec}，厂家：{firmId}，药房：{dispensary}");
                decimal totalStock = GetDrugInventoryInStorage(drugCode, drugSpec, firmId, dispensary);
                WriteDebugLog($"[ValidateStock] 库存查询结果 - 药品：{drugCode}，库存数量：{totalStock}");
                
                if (totalStock == -999)
                {
                    return new StockValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"获取药品【{drugName}】在药房【{dispensary}】的库存信息失败！",
                        TotalStock = 0,
                        AvailableStock = 0,
                        ReservedStock = 0
                    };
                }

                if (totalStock <= 0)
                {
                    return new StockValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"药品【{drugName}】在药房【{dispensary}】不可供，请确认！",
                        TotalStock = totalStock,
                        AvailableStock = 0,
                        ReservedStock = 0
                    };
                }

                // 2. 获取预扣库存（与门诊医生站完全相同的逻辑）
                // 临时禁用预扣库存查询，避免异常影响发药流程
                decimal reservedStock1 = 0; // GetDrugInventoryInNotBilling(drugCode, drugSpec, firmId, dispensary);
                decimal reservedStock2 = 0; // GetDrugInventoryInDispensing(drugCode, drugSpec, firmId, dispensary);
                decimal totalReservedStock = reservedStock1 + reservedStock2;

                WriteDebugLog($"[ValidateStock] 临时简化验证 - 药品：{drugName}({drugCode})，物理库存：{totalStock}，预扣库存：{totalReservedStock}（已禁用），可用库存：{totalStock - totalReservedStock}");

                // 3. 计算可用库存
                decimal availableStock = totalStock - totalReservedStock;

                if (availableStock < requiredQuantity)
                {
                    WriteDebugLog($"[ValidateStock] 库存不足 - 药品：{drugName}({drugCode})");
                    WriteDebugLog($"[ValidateStock] 物理库存：{totalStock:F2}{units}，预扣库存：{totalReservedStock:F2}{units}，可用库存：{availableStock:F2}{units}，需要数量：{requiredQuantity:F2}{units}");
                    WriteDebugLog($"[ValidateStock] ========== 验证结束（库存不足）==========");

                    return new StockValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"药品【{drugName}】库存不足！\n" +
                                     $"物理库存：{totalStock:F2}{units}\n" +
                                     $"预扣库存：{totalReservedStock:F2}{units}\n" +
                                     $"可用库存：{availableStock:F2}{units}\n" +
                                     $"需要数量：{requiredQuantity:F2}{units}",
                        TotalStock = totalStock,
                        AvailableStock = availableStock,
                        ReservedStock = totalReservedStock
                    };
                }

                WriteDebugLog($"[ValidateStock] 库存验证通过 - 药品：{drugName}({drugCode})");
                WriteDebugLog($"[ValidateStock] 物理库存：{totalStock:F2}{units}，预扣库存：{totalReservedStock:F2}{units}，可用库存：{availableStock:F2}{units}，需要数量：{requiredQuantity:F2}{units}");
                WriteDebugLog($"[ValidateStock] ========== 验证结束（成功）==========");

                return new StockValidationResult
                {
                    IsValid = true,
                    ErrorMessage = string.Empty,
                    TotalStock = totalStock,
                    AvailableStock = availableStock,
                    ReservedStock = totalReservedStock
                };
            }
            catch (Exception ex)
            {
                // 记录异常日志
                WriteDebugLog($"[ValidateStock] ========== 验证异常 ==========");
                WriteDebugLog($"[ValidateStock] 异常药品：{drugName}({drugCode})");
                WriteDebugLog($"[ValidateStock] 异常类型：{ex.GetType().Name}");
                WriteDebugLog($"[ValidateStock] 异常信息：{ex.Message}");
                WriteDebugLog($"[ValidateStock] 异常堆栈：{ex.StackTrace}");
                WriteDebugLog($"[ValidateStock] 内部异常：{ex.InnerException?.Message ?? "无"}");

                // 临时解决方案：当统一验证出现异常时，不阻止发药流程，但记录警告
                WriteDebugLog($"[ValidateStock] 警告 - 由于异常，跳过统一库存验证，允许发药继续 - 药品：{drugName}({drugCode})");
                WriteDebugLog($"[ValidateStock] ========== 验证结束（异常跳过）==========");

                return new StockValidationResult
                {
                    IsValid = true, // 临时设为true，不阻止发药
                    ErrorMessage = $"统一库存验证异常，已跳过验证：{ex.Message}",
                    TotalStock = 0,
                    AvailableStock = 0,
                    ReservedStock = 0
                };
            }
        }

        /// <summary>
        /// 查询药品库存（复制门诊医生站的逻辑）
        /// </summary>
        private static decimal GetDrugInventoryInStorage(string drugCode, string drugSpec, string firmId, string dispensary)
        {
            WriteDebugLog($"[GetDrugInventoryInStorage] 开始查询库存 - 药品：{drugCode}，规格：{drugSpec}，厂家：{firmId}，药房：{dispensary}");

            try
            {
                WriteDebugLog($"[GetDrugInventoryInStorage] 创建数据库连接...");
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();

                WriteDebugLog($"[GetDrugInventoryInStorage] 构建SQL查询...");
                StringBuilder builder = new StringBuilder("SELECT NVL(SUM(QUANTITY), 0) ");
                builder.Append(" FROM DRUG_STOCK  ");
                builder.Append(" WHERE DRUG_CODE = :DRUGCODE ");
                builder.Append(" AND PACKAGE_SPEC = :DRUGSPEC ");
                builder.Append(" AND FIRM_ID = :FRIMID");
                builder.Append(" AND STORAGE = :DISPENSARY");
                builder.Append(" AND SUPPLY_INDICATOR = '1' ");
                builder.Append(" AND HIS_UNIT_CODE = :HIS_UNIT_CODE");

                WriteDebugLog($"[GetDrugInventoryInStorage] SQL语句：{builder.ToString()}");

                WriteDebugLog($"[GetDrugInventoryInStorage] 设置查询参数...");
                List<string> para = new List<string>();
                ArrayList para_val = new ArrayList();
                para.Add("DRUGCODE");
                para.Add("DRUGSPEC");
                para.Add("FRIMID");
                para.Add("DISPENSARY");
                para.Add("HIS_UNIT_CODE");

                para_val.Add(drugCode);
                para_val.Add(drugSpec);
                para_val.Add(firmId);
                para_val.Add(dispensary);
                para_val.Add(SystemParm.HisUnitCode);

                WriteDebugLog($"[GetDrugInventoryInStorage] 参数值 - DRUGCODE:{drugCode}, DRUGSPEC:{drugSpec}, FRIMID:{firmId}, DISPENSARY:{dispensary}, HIS_UNIT_CODE:{SystemParm.HisUnitCode}");

                string sql = builder.ToString();
                WriteDebugLog($"[GetDrugInventoryInStorage] 执行数据库查询...");
                DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                WriteDebugLog($"[GetDrugInventoryInStorage] 查询执行完成");

                WriteDebugLog($"[GetDrugInventoryInStorage] 处理查询结果...");
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0][0] != DBNull.Value)
                {
                    decimal result = Convert.ToDecimal(ds.Tables[0].Rows[0][0]);
                    WriteDebugLog($"[GetDrugInventoryInStorage] 查询成功 - 药品：{drugCode}，库存数量：{result}");
                    return result;
                }
                WriteDebugLog($"[GetDrugInventoryInStorage] 查询失败 - 药品：{drugCode}，未找到有效数据");
                return -999; // 表示查询失败
            }
            catch (Exception ex)
            {
                WriteDebugLog($"[GetDrugInventoryInStorage] 异常 - 药品：{drugCode}，异常信息：{ex.Message}，堆栈：{ex.StackTrace}");
                return -999; // 返回错误值而不是抛出异常，避免中断发药流程
            }
        }

        /// <summary>
        /// 查询待收费数量（复制门诊医生站的逻辑）
        /// </summary>
        private static decimal GetDrugInventoryInNotBilling(string drugCode, string drugSpec, string firmId, string dispensary)
        {
            try
            {
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                StringBuilder builder = new StringBuilder("SELECT NVL(SUM(AMOUNT), 0) ");
                builder.Append(" FROM OUTP_ORDERS_COSTS ");
                builder.Append(" WHERE BILL_DATE IS NULL ");
                builder.Append(" AND ORDER_CODE = :DRUG_CODE");
                builder.Append(" AND ITEM_SPEC = :PACKAGE_SPEC ");
                builder.Append(" AND FIRM_ID = :FIRM_ID");
                builder.Append(" AND PERFORMED_BY = :DISPENSARY");
                builder.Append(" AND HIS_UNIT_CODE = :HIS_UNIT_CODE");

                List<string> para = new List<string>();
                ArrayList para_val = new ArrayList();
                para.Add("DRUG_CODE");
                para.Add("PACKAGE_SPEC");
                para.Add("FIRM_ID");
                para.Add("DISPENSARY");
                para.Add("HIS_UNIT_CODE");

                para_val.Add(drugCode);
                para_val.Add(drugSpec);
                para_val.Add(firmId);
                para_val.Add(dispensary);
                para_val.Add(SystemParm.HisUnitCode);

                string sql = builder.ToString();
                DataSet ds = spc.GetDataTable_Para(sql, para, para_val);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0][0] != DBNull.Value)
                {
                    return Convert.ToDecimal(ds.Tables[0].Rows[0][0]);
                }
                return 0;
            }
            catch (Exception ex)
            {
                WriteDebugLog($"[GetDrugInventoryInNotBilling] 异常 - 药品：{drugCode}，异常信息：{ex.Message}，堆栈：{ex.StackTrace}");
                return 0; // 异常时返回0，不影响主流程
            }
        }

        /// <summary>
        /// 查询待发药数量（复制门诊医生站的逻辑）
        /// </summary>
        private static decimal GetDrugInventoryInDispensing(string drugCode, string drugSpec, string firmId, string dispensary)
        {
            try
            {
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                StringBuilder builder = new StringBuilder("SELECT NVL(SUM(F.QUANTITY), 0) ");
                builder.Append(" FROM DRUG_PRESC_MASTER_TEMP E, DRUG_PRESC_DETAIL_TEMP F  ");
                builder.Append(" WHERE E.PRESC_DATE = F.PRESC_DATE ");
                builder.Append(" AND E.PRESC_NO = F.PRESC_NO ");
                builder.Append(" AND E.DISPENSARY = :DISPENSARY ");
                builder.Append(" AND F.FIRM_ID = :FIRM_ID");
                builder.Append(" AND F.PACKAGE_SPEC = :PACKAGE_SPEC ");
                builder.Append(" AND F.DRUG_CODE = :DRUG_CODE");
                builder.Append(" AND E.HIS_UNIT_CODE = :HIS_UNIT_CODE");
                builder.Append($" AND (SYSDATE - E.PRESC_DATE) <= (SELECT NVL(PARAMETER_VALUE, 7) FROM COMM.APP_CONFIGER_PARAMETER WHERE PARAMETER_NAME = 'DRUG_RESERVE_DAYS' AND APP_NAME = 'OUTPDOCT' AND HIS_UNIT_CODE = '{SystemParm.HisUnitCode}')");

                List<string> para = new List<string>();
                ArrayList para_val = new ArrayList();
                para.Add("DRUG_CODE");
                para.Add("PACKAGE_SPEC");
                para.Add("FIRM_ID");
                para.Add("DISPENSARY");
                para.Add("HIS_UNIT_CODE");

                para_val.Add(drugCode);
                para_val.Add(drugSpec);
                para_val.Add(firmId);
                para_val.Add(dispensary);
                para_val.Add(SystemParm.HisUnitCode);

                string sql = builder.ToString();
                DataSet ds = spc.GetDataTable_Para(sql, para, para_val);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0][0] != DBNull.Value)
                {
                    return Convert.ToDecimal(ds.Tables[0].Rows[0][0]);
                }
                return 0;
            }
            catch (Exception ex)
            {
                WriteDebugLog($"[GetDrugInventoryInDispensing] 异常 - 药品：{drugCode}，异常信息：{ex.Message}，堆栈：{ex.StackTrace}");
                return 0; // 异常时返回0，不影响主流程
            }
        }

        /// <summary>
        /// 写入调试日志
        /// </summary>
        private static void WriteDebugLog(string message)
        {
            try
            {
                string logPath = @"..\Client\LOG\exLOG\";
                if (!System.IO.Directory.Exists(logPath))
                {
                    System.IO.Directory.CreateDirectory(logPath);
                }

                string fileName = $"处方发药站_库存验证_{DateTime.Now:yyyyMMdd}.log";
                string fullPath = System.IO.Path.Combine(logPath, fileName);

                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [库存验证] {message}{Environment.NewLine}";
                System.IO.File.AppendAllText(fullPath, logEntry, Encoding.UTF8);

                // 【增强调试】同时写入到小时级别的详细日志文件
                string detailedFileName = $"处方发药站_库存验证_详细_{DateTime.Now:yyyyMMdd_HH}.log";
                string detailedFullPath = System.IO.Path.Combine(logPath, detailedFileName);
                System.IO.File.AppendAllText(detailedFullPath, logEntry, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 【增强调试】记录日志写入异常到系统事件日志
                try
                {
                    string errorLogPath = System.IO.Path.Combine(@"..\Client\LOG\exLOG\", $"库存验证_日志异常_{DateTime.Now:yyyyMMdd}.log");
                    string errorEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [日志异常] 写入日志失败：{ex.Message}{Environment.NewLine}原始消息：{message}{Environment.NewLine}";
                    System.IO.File.AppendAllText(errorLogPath, errorEntry, Encoding.UTF8);
                }
                catch
                {
                    // 最后的异常处理，不影响主流程
                }
            }
        }
    }

    /// <summary>
    /// 库存验证结果
    /// </summary>
    public class StockValidationResult
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 总库存
        /// </summary>
        public decimal TotalStock { get; set; }

        /// <summary>
        /// 可用库存
        /// </summary>
        public decimal AvailableStock { get; set; }

        /// <summary>
        /// 预扣库存
        /// </summary>
        public decimal ReservedStock { get; set; }
    }
}
