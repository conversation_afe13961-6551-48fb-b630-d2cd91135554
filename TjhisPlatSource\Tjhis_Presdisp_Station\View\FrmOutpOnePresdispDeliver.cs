﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using NM_Service.NMService;
//using PlatCommon.System;
using PlatCommon.Common;
using System.Runtime.InteropServices;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.Controls;
//using NursingPlatform.Presdisp.comm;
using DevExpress.XtraReports.UI;
using System.Data.Odbc;
using System.Data.OleDb;
using Tjhis.Presdisp.Station.Common;
using Tjhis.Presdisp.Station.Comm;
using DevExpress.XtraGrid.Views.Grid;
using Tjhis.Presdisp.Station.Report;
using Tjhis.Interface.Station;
using PlatCommon.SysBase;
using DevExpress.XtraReports.Parameters;
using PlatCommonForm;
using Tjhis.Interface.Station.IdentityCard;
using Tjhis.Interface.Station.HospitalCard;
using Tjhis.Presdisp.Station.PresDisp.Mode;
using System.Reflection;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using Tjhis.Interface.Station.Phstock;


namespace Tjhis.Presdisp.Station.View
{
    public partial class FrmOutpOnePresdispDeliver : PlatCommon.SysBase.ParentForm
    {
        #region 变量
        int showPrescDays = 500;//显示天数
        DataTable prescdt = new DataTable();//右侧处方列表数据集
        DataRow masterdr;//处方基础信息
        string prescqueue = "";
        string is_seatcode, is_windws_no; //桌席窗口号
        int il_id = 0;
        DataTable dt_queue_wait;
        string st_queue_wait, st_queue_status, st_client_status;
        string is_text;
        bool allow_confirm = false;
        DataRow prescdr_p;//处方基础信息
        DataTable detadt_p;
        string canshu = string.Empty;//合理用药开关
        int Load_State = 0; //合理用药初始化情况 1正常 其它都失败
        bool doPass = false; //是否执行合理用药
        string DRUG_TRACEABILITY_CODE = "0";//是否启用药品追溯码（1﹣启用，0﹣不启用）
        string NO_DRUG_TRACEABILITY_CODE = "0";//当没有查到药品追溯码时，业务是否继续进行（1﹣继续，0﹣不继续）
        string DRUG_TRACKABILITY_CODE_UPLOAD = string.Empty; //追溯码上传方式 0不上传，1实时上传，2后台定时上传
        /// <summary>
        /// 批次管理标志
        /// </summary>
        string managerBatchno;

        /// <summary>
        /// 语音提示相关变量
        /// </summary>
        private System.Windows.Forms.Timer voiceTimer;
        private int lastPrescCount = 0; // 上次处方数量
        private bool voiceEnabled = true; // 语音提示开关

        DateTime sysDateTime;
        DateTime DateTimeSYSDATE;

        /// <summary>
        /// 231020药品库存记录
        /// </summary>
        private DataTable stockdt;
        #endregion

        #region 事件
        public FrmOutpOnePresdispDeliver()
        {
            InitializeComponent();
            // gv2.hip

            // 初始化系统时间
            try
            {
                sysDateTime = new NM_Service.NMService.ServerPublicClient().GetSysDate();
            }
            catch
            {
                sysDateTime = DateTime.Now;
            }
        }

        /// <summary>
        /// Load事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmOutpOnePresdispDeliver_Load(object sender, EventArgs e)
        {
            DRUG_TRACEABILITY_CODE = PlatCommon.SysBase.SystemParm.GetParameterValue("DRUG_TRACEABILITY_CODE", "*", "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode);
            NO_DRUG_TRACEABILITY_CODE = PlatCommon.SysBase.SystemParm.GetParameterValue("NO_DRUG_TRACEABILITY_CODE", "*", "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode);
            DRUG_TRACKABILITY_CODE_UPLOAD = PlatCommon.SysBase.SystemParm.GetParameterValue("DRUG_TRACKABILITY_CODE_UPLOAD".ToUpper(), "*", "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode);

            canshu = PlatCommon.SysBase.SystemParm.GetParameterValue("ISPASS", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            prescqueue = PlatCommon.SysBase.SystemParm.GetParameterValue("PRESCQUEUE", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            managerBatchno = PlatCommon.SysBase.SystemParm.GetParameterValue("MANAGERBATCHNO", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode); //批次管理标识
            try
            {
                is_windws_no = Utility.ConfigHelper.GetConfigString("DRUG_WINDOWS_NO").ToString();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("请先查看本地窗口号DRUG_WINDOWS_NO的配置 " + ex.Message);
            }
            if (string.IsNullOrEmpty(is_windws_no))
            {
                // rg_windws_check.EditValue = "0";
            }

            //System.Threading.Thread myThread = new System.Threading.Thread(InitData);//
            //myThread.Start();
            InitData();
            rg1.SelectedIndex = 2;
            rg1.SelectedIndex = 0;

            is_seatcode = is_windws_no;

            if (canshu.Equals("1"))
            {
                //合理用药初始化
                Load_State = PlatPublic.Common.MeiKang_Transfer.MeiKang_Load("mzyf", PlatCommon.SysBase.SystemParm.HospitalID, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
            }

            // 全景病历
            string show_Panoramic = PlatCommon.SysBase.SystemParm.GetParameterValue("SHOW_PANORAMIC", "*", "*", "*", PlatCommon.SysBase.SystemParm.HisUnitCode);
            if (show_Panoramic == "0")
            {
                barLargeButtonItem2.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }

            if (string.IsNullOrEmpty(is_text))
            {
                is_text = messageinfo.Text;
            }
            init_queue_call();
            gv1.Focus();

            //231020获取库存表结构
            GetDrugStock("", "", "", "", "", "", "", "", ref stockdt);

            if (DRUG_TRACEABILITY_CODE == "1")
            {
                colCaoZuo.Visible = true;
            }
            else
            {
                colCaoZuo.Visible = false;
            }

            // 初始化语音提示功能
            InitVoiceAlert();
        }


        /// <summary>
        /// 确定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BbtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (masterdr == null) return;
                if (gv2.RowCount < 0 || txtPatientID.Text.Length < 1) return;
                //保存时是否弹出确认提示框
                string commitConfirm = PlatCommon.SysBase.SystemParm.GetParameterValue("COMMIT_CONFIRM", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (commitConfirm.Length > 0 && Convert.ToInt16(commitConfirm) > 0)
                {
                    if (XtraMessageBox.Show("是否确认？", "提示", MessageBoxButtons.OKCancel) == DialogResult.Cancel)
                    {
                        return;
                    }
                }
                //是否重新输入密码
                string confirmlogin = PlatCommon.SysBase.SystemParm.GetParameterValue("CONFIRM_LOGIN", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                if (confirmlogin == "1")
                {
                    Comm.FrmPrescLogin presclogin = new Comm.FrmPrescLogin();//1
                    presclogin.ShowDialog();
                    if (!presclogin.resultstr) return;
                }
                if (txtPrescAttr.Text == "")//医生站开处方，药房进行审核，所以必须检查属性类型
                {
                    string prescattr = PlatCommon.SysBase.SystemParm.GetParameterValue("PRESC_ATTR", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    if (prescattr == "1")
                    {
                        Comm.FrmPrescAttr prescat = new Comm.FrmPrescAttr();
                        prescat.ShowDialog();
                        string strattr = prescat.dr_attr;
                        if (strattr != "") txtPrescAttr.Text = strattr;
                    }
                }

                string prescDate = masterdr["PRESC_DATE"].ToString();
                string prescNo = masterdr["PRESC_NO"].ToString();
                string sql1 = "select count(*) from drug_presc_master_temp where presc_date=to_date('" + prescDate + "','yyyy-mm-dd hh24:mi:ss')  and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' and presc_no=" + prescNo;
                DataTable dtCount = new ServerPublicClient().GetDataBySql(sql1).Tables[0];
                if (dtCount == null || dtCount.Rows.Count < 1)
                {
                    MessageBox.Show("查询待发药处方失败", "提示");
                    return;
                }
                if (dtCount.Rows[0][0].ToString().Equals("0"))
                {
                    MessageBox.Show("查询待发药处方失败,请确认是不是收款处已经退费了!", "提示");
                    return;
                }
                //添加记录
                Dictionary<string, string> allsave = new Dictionary<string, string>();
                DataTable detaildt = (DataTable)gc2.DataSource;

                // 2025-01-03 修复：获取处方指定的药局，而不是使用当前发药科室
                string prescDispensary = masterdr["DISPENSARY"].ToString();
                if (string.IsNullOrEmpty(prescDispensary))
                {
                    prescDispensary = this.DeptCode; // 如果处方中没有指定药局，使用当前发药科室作为备选
                    Utility.LogFile.WriteLogAuto($"处方{masterdr["PRESC_NO"]}未指定DISPENSARY，使用当前发药科室{this.DeptCode}", "处方发药");
                }
                else
                {
                    Utility.LogFile.WriteLogAuto($"处方{masterdr["PRESC_NO"]}指定DISPENSARY={prescDispensary}，当前发药科室={this.DeptCode}", "处方发药");
                }
                //DataTable stockdt = new DataTable();
                //231020
                stockdt.Clear();
                string strFilter, strFilterBatchNo;
                DataRow[] drFilterRows;
                DataRow drDrugSotck;
                decimal stockcount;//库存数量
                decimal detailcount;//当前使用数量
                decimal onecount;//每次减的数量，第一条库存不够，为第一条数量，第一条够了，为使用数量
                decimal maxitem_no;//当条处方最大item_no
                DataTable insertdetaildt = GetPrescDetailDataRow();
                string sql = "";
                ////添加主记录
                //sql = InsertDrugPrescMaster(masterdr);
                //if (!allsave.ContainsKey(sql))
                //{
                //    allsave.Add(sql, "新增处方主记录失败！");
                //}
                decimal ldcCosts = 0;
                decimal ldcCharges = 0;
                maxitem_no = 0;
                //添加明细记录
                for (int i = 0; i < detaildt.Rows.Count; i++)
                {
                    //获取库存信息 
                    string batchNo = detaildt.Rows[i]["BATCH_NO"].ToString();
                    string batchCode = detaildt.Rows[i]["BATCH_CODE"].ToString();
                    if (!"1".Equals(managerBatchno))
                    {
                        batchCode = "";
                        batchNo = "";
                    }

                    // 2025-01-03 修复：使用处方指定的药局查询库存，而不是当前发药科室
                    GetDrugStock(prescDispensary, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["DRUG_SPEC"].ToString(),
                        detaildt.Rows[i]["PACKAGE_SPEC"].ToString(), detaildt.Rows[i]["FIRM_ID"].ToString(),
                        batchNo, batchCode, detaildt.Rows[i]["RETAIL_PRICE"].ToString(), ref stockdt);
                    if (stockdt.Rows.Count < 1)//没有库存
                    {

                        XtraMessageBox.Show("药品" + detaildt.Rows[i]["DRUG_NAME"] + "的库存不够!\r\n" + "批次BATCH_NO: " + detaildt.Rows[i]["BATCH_NO"].ToString() + "批号BATCH_CODE: " + detaildt.Rows[i]["BATCH_CODE"].ToString());
                        return;
                    }

                    //231020获取存在的药品库存信息
                    //如果要加批次等过滤条件那就自己加吧
                    strFilter = "DRUG_CODE='" + detaildt.Rows[i]["DRUG_CODE"] + "' and DRUG_SPEC='" + detaildt.Rows[i]["DRUG_SPEC"] + "' and PACKAGE_SPEC='" + detaildt.Rows[i]["PACKAGE_SPEC"] + "' and FIRM_ID='" + detaildt.Rows[i]["FIRM_ID"] + "'";
                    if (!string.IsNullOrEmpty(batchNo) && batchNo != "X")
                    {
                        //查看分配的批次是否有库存，没有随机匹配其它批次
                        strFilterBatchNo = strFilter + " AND BATCH_NO='" + batchNo + "'";
                        drFilterRows = stockdt.Select(strFilterBatchNo, "EXPIRE_DATE asc, BATCH_NO asc");
                        if (drFilterRows.Length < 1)
                            drFilterRows = stockdt.Select(strFilter, "EXPIRE_DATE asc, BATCH_NO asc");
                    }
                    else
                    {
                        drFilterRows = stockdt.Select(strFilter, "EXPIRE_DATE asc, BATCH_NO asc");
                    }
                    if (drFilterRows.Length < 1)
                    {
                        //获取药品库存信息
                        // 2025-01-03 修复：使用处方指定的药局查询库存，而不是当前发药科室
                        GetDrugStock(prescDispensary, detaildt.Rows[i]["DRUG_CODE"].ToString(), detaildt.Rows[i]["DRUG_SPEC"].ToString(),
                            detaildt.Rows[i]["PACKAGE_SPEC"].ToString(), detaildt.Rows[i]["FIRM_ID"].ToString(),
                            batchNo, batchCode, detaildt.Rows[i]["RETAIL_PRICE"].ToString(), ref stockdt);
                        drFilterRows = stockdt.Select(strFilter, "EXPIRE_DATE asc, BATCH_NO asc");
                    }
                    if (drFilterRows.Length < 1)
                    {
                        XtraMessageBox.Show("未找到药品" + detaildt.Rows[i]["DRUG_NAME"].ToString() + "或者该药品不可供!", "提示");
                        return;
                    }

                    detailcount = Convert.ToDecimal(detaildt.Rows[i]["QUANTITY"]);//申请数量

                    bool result = false;
                    if ("1".Equals(managerBatchno))
                    {
                        //for (int s = 0; s < stockdt.Rows.Count; s++)
                        //{
                        //    stockcount = Convert.ToDecimal(stockdt.Rows[s]["QUANTITY"]);//库存数量
                        //    if (stockcount >= detailcount)
                        //    {
                        //        result = true;
                        //        break;
                        //    }
                        //}
                        stockcount = drFilterRows.Sum(s => Convert.ToDecimal(s["QUANTITY"]));
                        if (stockcount >= detailcount)
                        {
                            result = true;
                        }
                    }
                    else
                    {
                        //stockcount = Convert.ToDecimal(stockdt.Compute("sum(QUANTITY)", "true"));//多条库存总数量
                        stockcount = drFilterRows.Sum(s => Convert.ToDecimal(s["QUANTITY"]));
                        if (stockcount >= detailcount)
                        {
                            result = true;
                        }
                    }
                    if (!result)
                    {
                        XtraMessageBox.Show("药品" + detaildt.Rows[i]["DRUG_NAME"] + "的库存数量不够!\r\n");
                        return;
                    }

                    // 【新增】统一库存验证 - 与门诊医生站保持一致的库存计算逻辑
                    string drugCode = detaildt.Rows[i]["DRUG_CODE"].ToString();
                    string drugName = detaildt.Rows[i]["DRUG_NAME"].ToString();
                    string drugSpec = detaildt.Rows[i]["DRUG_SPEC"].ToString();
                    string packageSpec = detaildt.Rows[i]["PACKAGE_SPEC"].ToString();
                    string firmId = detaildt.Rows[i]["FIRM_ID"].ToString();
                    string units = detaildt.Rows[i]["PACKAGE_UNITS"].ToString();

                    StockValidationResult validationResult = UnifiedStockValidator.ValidateStock(
                        drugCode, drugName, packageSpec, firmId, prescDispensary, detailcount, units);

                    if (!validationResult.IsValid)
                    {
                        XtraMessageBox.Show($"【统一库存验证】{validationResult.ErrorMessage}\n处方号：{masterdr["PRESC_NO"]}", "库存验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    //分批减库存
                    for (int j = 0; j < drFilterRows.Length; j++)
                    //for (int j = 0; j < stockdt.Rows.Count; j++)
                    {
                        if (detailcount == 0) break;
                        drDrugSotck = drFilterRows[j];
                        stockcount = Convert.ToDecimal(drDrugSotck["QUANTITY"]);//库存数量
                        if (stockcount == 0) continue;//医生站开处方开俩条相同的药品，在处方发药的时候这俩条记录分布在不同的批次上,发完第一个药品后，库存恰好变为0，防止在处方表中插入一条发药数量为0的处方记录	
                        if ("1".Equals(managerBatchno))
                        {
                            if (stockcount < detailcount)
                            {
                                continue;//如果管理到批次，数量不足的库存跳过
                            }
                            else
                            {
                                onecount = detailcount;
                                sql = UpdateDrugStock(onecount, this.DeptCode, drDrugSotck["DRUG_CODE"].ToString(),
                                drDrugSotck["DRUG_SPEC"].ToString(),
                                drDrugSotck["PACKAGE_SPEC"].ToString(), drDrugSotck["FIRM_ID"].ToString(),
                                drDrugSotck["BATCH_NO"].ToString(), drDrugSotck["BATCH_CODE"].ToString(), drDrugSotck["RETAIL_PRICE"].ToString());
                                detailcount = 0;
                            }

                        }
                        else
                        {
                            if (stockcount < detailcount)
                            {
                                detailcount = detailcount - stockcount;
                                onecount = stockcount;
                                sql = UpdateDrugStock(onecount, this.DeptCode, drDrugSotck["DRUG_CODE"].ToString(),
                                    drDrugSotck["DRUG_SPEC"].ToString(),
                                    drDrugSotck["PACKAGE_SPEC"].ToString(), drDrugSotck["FIRM_ID"].ToString(),
                                    drDrugSotck["BATCH_NO"].ToString(), drDrugSotck["BATCH_CODE"].ToString(), detaildt.Rows[i]["RETAIL_PRICE"].ToString());
                            }
                            else
                            {
                                onecount = detailcount;
                                detailcount = 0;
                                sql = UpdateDrugStock(onecount, this.DeptCode, drDrugSotck["DRUG_CODE"].ToString(),
                                    drDrugSotck["DRUG_SPEC"].ToString(),
                                    drDrugSotck["PACKAGE_SPEC"].ToString(), drDrugSotck["FIRM_ID"].ToString(),
                                    drDrugSotck["BATCH_NO"].ToString(), drDrugSotck["BATCH_CODE"].ToString(), drDrugSotck["RETAIL_PRICE"].ToString());
                            }
                        }
                        drDrugSotck["QUANTITY"] = stockcount - onecount;

                        //增加条件 防止sql重复 by lions 2019-03-30
                        sql += " and " + i + "=" + i;
                        if (maxitem_no >= 0)
                        {
                            maxitem_no = maxitem_no + 1;
                        }
                        if (!allsave.ContainsKey(sql))
                        {
                            allsave.Add(sql, "修改药品库存失败！");
                        }
                        DataRow detadr = insertdetaildt.NewRow();
                        detadr["PRESC_DATE"] = detaildt.Rows[i]["PRESC_DATE"];
                        detadr["PRESC_NO"] = detaildt.Rows[i]["PRESC_NO"];
                        detadr["ITEM_NO"] = maxitem_no;
                        detadr["DRUG_CODE"] = detaildt.Rows[i]["DRUG_CODE"];
                        detadr["DRUG_NAME"] = drDrugSotck["DRUG_NAME"];
                        detadr["TRADE_NAME"] = drDrugSotck["TRADE_NAME"];
                        detadr["DRUG_SPEC"] = detaildt.Rows[i]["DRUG_SPEC"];
                        detadr["UNITS"] = detaildt.Rows[i]["UNITS"];
                        detadr["PACKAGE_SPEC"] = detaildt.Rows[i]["PACKAGE_SPEC"];
                        detadr["PACKAGE_UNITS"] = detaildt.Rows[i]["PACKAGE_UNITS"];
                        detadr["FIRM_ID"] = detaildt.Rows[i]["FIRM_ID"];
                        detadr["BATCH_NO"] = drDrugSotck["BATCH_NO"];
                        detadr["GEN_SERIAL"] = GetGenSerialSeq();
                        detadr["QUANTITY"] = onecount;//detailcount;
                        detadr["INVENTORY"] = stockcount - onecount; //stockcount - detailcount;
                        detadr["BATCH_CODE"] = drDrugSotck["BATCH_CODE"];
                        detadr["EXPIRE_DATE"] = drDrugSotck["EXPIRE_DATE"] == DBNull.Value ? "" : Convert.ToDateTime(drDrugSotck["EXPIRE_DATE"]).ToString("yyyy-MM-dd");
                        detadr["PURCHASE_PRICE"] = drDrugSotck["PURCHASE_PRICE"];
                        detadr["TRADE_PRICE"] = drDrugSotck["TRADE_PRICE"];
                        detadr["RETAIL_PRICE"] = drDrugSotck["RETAIL_PRICE"];
                        detadr["SUPPLIER"] = drDrugSotck["SUPPLIER"];
                        detadr["COSTS"] = (Convert.ToDecimal(detadr["QUANTITY"]) * Convert.ToDecimal(drDrugSotck["RETAIL_PRICE"]));
                        detadr["PAYMENTS"] = (Convert.ToDecimal(detadr["QUANTITY"]) * Convert.ToDecimal(drDrugSotck["RETAIL_PRICE"]));
                        detadr["ROUND_AMT"] = detaildt.Rows[i]["ROUND_AMT"];
                        detadr["ORDER_NO"] = detaildt.Rows[i]["ORDER_NO"];
                        detadr["ORDER_SUB_NO"] = detaildt.Rows[i]["ORDER_SUB_NO"];
                        detadr["ADMINISTRATION"] = detaildt.Rows[i]["ADMINISTRATION"];
                        detadr["DOSAGE_EACH"] = detaildt.Rows[i]["DOSAGE_EACH"];
                        detadr["DOSAGE_UNITS"] = detaildt.Rows[i]["DOSAGE_UNITS"];
                        detadr["FREQUENCY"] = detaildt.Rows[i]["FREQUENCY"];
                        detadr["FREQ_DETAIL"] = detaildt.Rows[i]["FREQ_DETAIL"];
                        detadr["HANDBACK_AMOUNT"] = 0;
                        detadr["INSUR_ADULT"] = detaildt.Rows[i]["INSUR_ADULT"];
                        detadr["GUID"] = detaildt.Rows[i]["GUID"];
                        //detadr["OTC"] = detaildt.Rows[i]["OTC"];
                        //detadr["INSUR_SEQ"] = detaildt.Rows[i]["INSUR_SEQ"];
                        //detadr["CZ"] = detaildt.Rows[i]["CZ"];
                        //detadr["PRODUCE_DATE"] = detaildt.Rows[i]["PRODUCE_DATE"].ToString();
                        //detadr["DRUG_INDICATOR"] = detaildt.Rows[i]["DRUG_INDICATOR"].ToString();
                        //insertdetaildt.Rows.Add(detadr);
                        //---------------------------------------------------------
                        // 2025-01-03 修复：使用与门诊医生站完全一致的零售价计算费用
                        decimal consistentRetailPrice = GetDrugStockRetailPriceConsistent(
                            detadr["DRUG_CODE"].ToString(),
                            detadr["PACKAGE_SPEC"].ToString(),
                            detadr["FIRM_ID"].ToString(),
                            prescDispensary); // 使用处方指定的药局

                        // 如果获取一致性价格失败，则使用库存查询结果中的价格作为备选
                        decimal retailPrice = consistentRetailPrice > 0 ? consistentRetailPrice : Convert.ToDecimal(drDrugSotck["RETAIL_PRICE"]);

                        ldcCosts += (Convert.ToDecimal(detadr["QUANTITY"]) * retailPrice);
                        ldcCharges += (Convert.ToDecimal(detadr["QUANTITY"]) * retailPrice);

                        // 记录价格来源日志
                        Utility.LogFile.WriteLogAuto($"处方发药费用计算：药品{detadr["DRUG_CODE"]} 一致性价格={consistentRetailPrice} 库存价格={drDrugSotck["RETAIL_PRICE"]} 最终使用={retailPrice}", "处方发药");

                        sql = InsertDrugPrescDetail(detadr);
                        if (!allsave.ContainsKey(sql))
                        {
                            allsave.Add(sql, "新增处方明细记录失败！");
                        }
                        //stockcount = stockcount - detailcount;


                    }


                }

                //添加主记录
                masterdr["COSTS"] = ldcCosts;
                masterdr["PAYMENTS"] = ldcCharges;
                masterdr["sex"] = txtSex.EditValue.ToString();
                if (string.IsNullOrEmpty(txtBirth.Text.ToString()))
                {
                    masterdr["age"] = DBNull.Value;
                }
                else
                {
                    masterdr["age"] = int.Parse(txtBirth.Text.ToString());
                }
                masterdr["dispensing_usercode"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                masterdr["dispensing_provider"] = PlatCommon.SysBase.SystemParm.LoginUser.NAME;
                masterdr["dispensing_datetime"] = new ServerPublicClient().GetSysDate();
                sql = InsertDrugPrescMaster(masterdr);
                if (!allsave.ContainsKey(sql))
                {
                    allsave.Add(sql, "新增处方主记录失败FrmOutpOnePresdispDeliver！");
                }
                //sql = DeleteDrugPrescDetailTemp(gv1.GetRowCellValue(gv1.FocusedRowHandle, "PRESC_DATE").ToString(), gv1.GetRowCellValue(gv1.GetSelectedRows()[0], "PRESC_NO").ToString());
                sql = DeleteDrugPrescDetailTemp(masterdr["PRESC_DATE"].ToString(), masterdr["PRESC_NO"].ToString());
                if (!allsave.ContainsKey(sql))
                {
                    allsave.Add(sql, "删除门诊待发药处方明细表失败FrmOutpOnePresdispDeliver！");
                }
                sql = DeleteDrugPrescMasterTemp(masterdr["PRESC_DATE"].ToString(), masterdr["PRESC_NO"].ToString());
                if (!allsave.ContainsKey(sql))
                {
                    allsave.Add(sql, "删除门诊待发药处方主表失败！");
                }
                if (prescqueue == "1")
                {
                    sql = UpdateDrugPrescQueue(masterdr["PRESC_DATE"].ToString(), masterdr["PRESC_NO"].ToString());
                    if (!allsave.ContainsKey(sql))
                        allsave.Add(sql, "修改发药状态失败！");

                    sql = PrescQueueSeatDict();
                    if (!allsave.ContainsKey(sql))
                        allsave.Add(sql, "修改座位失败！");
                }
                //删除待发药队列！
                sql = DeletePrescQueue(masterdr["PRESC_DATE"].ToString(), masterdr["PRESC_NO"].ToString());
                if (!allsave.ContainsKey(sql))
                    allsave.Add(sql, "删除待发药队列失败！");
                //处理追溯码
                if (DRUG_TRACEABILITY_CODE == "1")
                {
                    for (int i = 0; i < detaildt.Rows.Count; i++)
                    {
                        DataRow item3505 = detaildt.Rows[i];
                        string TRACE_CODES = item3505["CZ"].ToString();
                        if (TRACE_CODES != "")
                        {

                            string drug_code = detaildt.Rows[i]["DRUG_CODE"].ToString();
                            string drug_spec = item3505["DRUG_SPEC"].ToString();
                            string package_spec = item3505["PACKAGE_SPEC"].ToString();
                            string FIRM_ID = item3505["FIRM_ID"].ToString();
                            string unit = item3505["PACKAGE_UNITS"].ToString();
                            string DRUG_INDICATOR = item3505["DRUG_INDICATOR"].ToString();
                            string PRESC_DATE = item3505["PRESC_DATE"].ToString();
                            string PRESC_NO = item3505["PRESC_NO"].ToString();
                            string QUANTITY = item3505["QUANTITY"].ToString();
                            string[] arrayCodes = TRACE_CODES.Split('|');
                            DRUG_TRACKABILITY_CODE model = new DRUG_TRACKABILITY_CODE();
                            model.DRUG_CODE = drug_code;
                            model.DRUG_SPEC = package_spec;
                            model.FIRM_ID = FIRM_ID;
                            model.UNITS = unit;
                            bool isResult = true;
                            string traceCode = "";
                            foreach (string s in arrayCodes)
                            {
                                model.DRUG_TRACE_CODE = s;
                                if (!TraceCode.GetDRUG_TRACKABILITY_CODE(model))
                                {
                                    traceCode = s;
                                    isResult = false;
                                    break;
                                }
                            }
                            if (!isResult && NO_DRUG_TRACEABILITY_CODE == "0")
                            {
                                XtraMessageBox.Show("规格:" + drug_spec + "厂家：" + FIRM_ID + "药品的追溯码【" + traceCode + "】不存在！");
                                return;
                            }

                            foreach (string s in arrayCodes)
                            {
                                DRUG_PRESC_TRACK prescTrack = new DRUG_PRESC_TRACK();
                                prescTrack.TRACK_ID = Guid.NewGuid().ToString("N").ToUpper();
                                prescTrack.TRACK_CODE = PRESC_DATE + "_" + PRESC_NO;
                                prescTrack.DRUG_CODE = drug_code;
                                prescTrack.DRUG_SPEC = package_spec;
                                prescTrack.FIRM_ID = FIRM_ID;
                                prescTrack.UNITS = unit;
                                prescTrack.TRACK_TYPE = "门诊发药";
                                prescTrack.QUANTITY = "1";
                                prescTrack.DRUG_TRACE_CODE = s;
                                prescTrack.OPER_DATE = sysDateTime;
                                prescTrack.DRUG_INDICATOR = DRUG_INDICATOR;
                                sql = PubComm.InsertDRUG_PRESC_TRACK(prescTrack);
                                if (!allsave.ContainsKey(sql))
                                {
                                    allsave.Add(sql, "新增药品处方追溯记录明细失败！");
                                }
                            }
                            model.QUANTITY = QUANTITY;
                            //if (TraceCode.DaXiaoBaoZhuang(model) > 1)//大包装
                            if(TraceCode.IsSplitPackage(model).Equals("0"))
                            {
                                foreach (string s in arrayCodes)
                                {
                                    model.DRUG_TRACE_CODE = s;
                                    DataTable dt = TraceCode.GetDrugTrackCodeBig(model);
                                    foreach (DataRow dr in dt.Rows)
                                    {
                                        model.TRACK_ID = dr["TRACK_ID"].ToString();
                                        sql = TraceCode.DelDRUG_TRACKABILITY_CODE(model);
                                        if (!allsave.ContainsKey(sql))
                                        {
                                            allsave.Add(sql, "删除药品追溯码表记录失败！");
                                        }
                                    }
                                }
                            }
                            else
                            {
                                model.DRUG_TRACE_CODE = arrayCodes[0];
                                DataTable dt = TraceCode.GetDrugTrackCodeSmall(model);
                                foreach (DataRow dr in dt.Rows)
                                {
                                    model.TRACK_ID = dr["TRACK_ID"].ToString();
                                    sql = TraceCode.DelDRUG_TRACKABILITY_CODE(model);
                                    if (!allsave.ContainsKey(sql))
                                    {
                                        allsave.Add(sql, "删除药品追溯码表记录失败！");
                                    }
                                }
                            }

                            DataSet ds3505 = new DataSet();//医保上传

                            //DataTable dsBase = new DataTable("base");
                            //dsBase.Columns.Add("sel_channel");
                            //dsBase.Columns.Add("jylx");
                            //DataRow drBase = dsBase.NewRow();
                            //drBase["sel_channel"] = "医院";
                            //drBase["jylx"] = "goods_Dict3505";
                            //dsBase.Rows.Add(drBase);
                            //ds3505.Tables.Add(dsBase);

                            DataTable dt3505 = new DataTable("selinfo");
                            //DataTable dt3505 = new DataTable("3505");
                            PubComm.SetColumns3505(ref dt3505);
                            DataRow dr3505 = dt3505.NewRow();
                            dr3505["med_list_codg"] = item3505["INSUR_SEQ"].ToString();
                            dr3505["fixmedins_hilist_id"] = item3505["drug_code"].ToString();
                            dr3505["fixmedins_hilist_name"] = item3505["drug_name"].ToString();
                            dr3505["fixmedins_bchno"] = item3505["batch_no"].ToString();
                            dr3505["prsc_dr_cert_type"] = "";
                            dr3505["prsc_dr_certno"] = "";
                            dr3505["prsc_dr_name"] = masterdr["PRESCRIBED_BY"].ToString();
                            dr3505["phar_cert_type"] = "";
                            dr3505["phar_certno"] = "";
                            dr3505["phar_name"] = masterdr["PRESCRIBED_BY"].ToString();//药师姓名
                            dr3505["phar_prac_cert_no"] = masterdr["WORK_ID"].ToString();//药师执业资格证号
                            dr3505["hi_feesetl_type"] = "";
                            dr3505["setl_id"] = "";
                            dr3505["mdtrt_sn"] = masterdr["CLINIC_NO"].ToString();//就医流水号-
                            dr3505["psn_no"] = "";
                            dr3505["psn_cert_type"] = "";
                            dr3505["certno"] = "";
                            dr3505["psn_name"] = "";
                            dr3505["manu_lotnum"] = item3505["batch_code"].ToString();//批号
                            dr3505["manu_date"] = item3505["PRODUCE_DATE"].ToString() == "" ? DateTime.Now.AddMonths(-2).ToString("yyyy-MM-dd") : item3505["PRODUCE_DATE"].ToString("yyyy-MM-dd");//PRODUCE_DATE
                            dr3505["expy_end"] = DateTime.Now.ToString("yyyy-MM-dd");
                            dr3505["rx_flag"] = (item3505["otc"] == DBNull.Value ? "0" : item3505["otc"].ToString());//-otc
                            DRUG_TRACKABILITY_CODE model1 = new DRUG_TRACKABILITY_CODE();
                            model1.DRUG_CODE = item3505["DRUG_CODE"].ToString();
                            model1.DRUG_SPEC = item3505["PACKAGE_SPEC"].ToString();
                            model1.FIRM_ID = item3505["FIRM_ID"].ToString();
                            //if (TraceCode.DaXiaoBaoZhuang(model1) > 1)
                            //{
                            //    dr3505["trdn_flag"] = "1";//拆零标志-
                            //}
                            //else
                            //{
                            //    dr3505["trdn_flag"] = "0";//拆零标志-
                            //}
                            dr3505["trdn_flag"] = TraceCode.IsSplitPackage(model1);//拆零标志-

                            decimal inventory = PubComm.GetCurrentStockNum(this.DeptCode, drug_code, drug_spec, package_spec, FIRM_ID);
                            dr3505["finl_trns_pric"] = 0;//0
                            dr3505["rxno"] = item3505["PRESC_NO"].ToString();
                            dr3505["rx_circ_flag"] = "";
                            dr3505["rtal_docno"] = masterdr["RCPT_NO"].ToString() + item3505["ITEM_NO"].ToString();//零售单据号-
                            dr3505["stoout_no"] = "";
                            dr3505["bchno"] = "";
                            dr3505["sel_detail_no"] = masterdr["RCPT_NO"].ToString() + item3505["ITEM_NO"].ToString();//销售明细流水号-
                            dr3505["drug_prod_barc"] = "";
                            dr3505["shelf_posi"] = "";
                            dr3505["sel_retn_cnt"] = item3505["quantity"].ToString();//数量
                            dr3505["sel_retn_time"] = DateTime.Parse(item3505["PRESC_DATE"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//PRESC_DATE
                            dr3505["sel_retn_opter_name"] = masterdr["ENTERED_BY"].ToString();//经办人姓名ENTERED_BY
                            dr3505["memo"] = "";
                            dr3505["inv_cnt"] = inventory - decimal.Parse(QUANTITY);//INVENTORY
                            dr3505["mdtrt_setl_type"] = masterdr["CHARGE_TYPE"].ToString();//费别
                            dr3505["sel_channel"] = "医院";//渠道-
                            //dr3505["jylx"] = "3505";
                            dr3505["jylx"] = "goods_Dict3505";
                            dt3505.Rows.Add(dr3505);
                            ds3505.Tables.Add(dt3505);
                            DataTable dtdrugtracinfo = new DataTable("drugtracinfo");
                            dtdrugtracinfo.Columns.Add("drug_trac_codg", typeof(string));
                            string[] traceCodes = TRACE_CODES.Split('|');
                            foreach (string str in traceCodes)
                            {
                                DataRow drdrugtracinfo = dtdrugtracinfo.NewRow();
                                drdrugtracinfo["drug_trac_codg"] = str;
                                dtdrugtracinfo.Rows.Add(drdrugtracinfo);
                            }
                            ds3505.Tables.Add(dtdrugtracinfo);
                            if (DRUG_TRACKABILITY_CODE_UPLOAD.Equals("1"))
                            {
                                if (TraceCode.UploadData(masterdr["CHARGE_TYPE"].ToString(), ds3505))
                                {
                                    //string sqlUpload = string.Empty;
                                    //更新上传标志
                                    foreach (DataRow drug in dtdrugtracinfo.Rows)
                                    {
                                        sql = "UPDATE DRUG_PRESC_TRACK SET UPLOAD_FLAG=1 WHERE DRUG_TRACE_CODE='" + drug["drug_trac_codg"] + "'";
                                        if (!allsave.ContainsKey(sql))
                                        {
                                            allsave.Add(sql, "更新DRUG_IMPORT_TRACK上传标志失败！");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }


                if (allsave.Count > 0)
                {
                    string result = new ServerPublicClient().SaveTable(allsave);
                    if (result.Length > 0)
                    {
                        XtraMessageBox.Show(result);
                    }
                    else
                    {
                        // 2025-07-20 恢复发药成功弹窗提示
                        XtraMessageBox.Show("处方发药成功！", "提示");

                        // 记录发药成功日志
                        WriteDispenseSuccessLog(masterdr);

                        prescdr_p = masterdr;
                        detadt_p = (DataTable)gc2.DataSource;
                        string prnflag = PlatCommon.SysBase.SystemParm.GetParameterValue("PRNFLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                        if (prnflag.Equals("1"))
                        {
                            Wf_Print_New(); //增加处方打印
                        }
                        else if (prnflag.Equals("2"))
                        {
                            if (XtraMessageBox.Show("是否打印?", "提示", MessageBoxButtons.YesNo) == DialogResult.Yes)
                            {
                                Wf_Print_New(); //增加处方打印
                            }
                        }
                        string prescdatenow = masterdr["PRESC_DATE"].ToString();
                        string prescnonow = prescdr_p["PRESC_NO"].ToString();//当前处方号

                        #region 统一接口 2022-04-14
                        string strSQL = " SELECT * FROM DRUG_PRESC_MASTER " +
                            " WHERE PRESC_DATE = TO_DATE('" + prescdatenow + "','YYYY-MM-DD HH24:MI:SS') " +
                            " AND PRESC_NO = '" + prescnonow + "'";
                        DataTable dataTableTEMP = new NM_Service.NMService.ServerPublicClient().GetDataBySql(strSQL).Tables[0];
                        DataTable[] dataTables = new DataTable[] { dataTableTEMP };
                        string strERROR_TEXT = string.Empty;
                        Tjhis.Interface.Station.Interface_Common.InvokeInterface("Presdisp_001", "UPDATE", this.AppName, this.DeptCode, dataTables, ref strERROR_TEXT);
                        #endregion

                        int idx = gv1.FocusedRowHandle;//当前选中行
                                                       //prescdt.Rows.Remove(prescdt.Select("PRESC_NO='" + prescnonow + "'")[0]);
                                                       //保存成功后，不刷新，删除保存成功的处方
                                                       //System.Threading.Thread myThread = new System.Threading.Thread(InitData);//20210709
                                                       //myThread.Start();
                        InitData();//只初始化左边处方列表
                        if (gv1.DataRowCount > 0)
                        {
                            if (rg1.SelectedIndex == 2)
                            {
                                gc1.DataSource = prescdt;
                                //bbtnSave.Enabled = false;
                            }
                            else if (rg1.SelectedIndex == 1)//过期处方
                            {
                                DataTable olddt = prescdt.Clone();
                                for (int i = 0; i < prescdt.Rows.Count; i++)
                                {
                                    if (SetNewOrOldPresc(Convert.ToDateTime(prescdt.Rows[i]["PRESC_DATE"])) == 0)
                                    {
                                        olddt.Rows.Add(prescdt.Rows[i].ItemArray);
                                    }
                                }
                                gc1.DataSource = olddt;
                                //bbtnSave.Enabled = false;
                            }
                            else if (rg1.SelectedIndex == 0)//当前处方
                            {
                                DataTable newdt = prescdt.Clone();
                                for (int i = 0; i < prescdt.Rows.Count; i++)
                                {
                                    if (SetNewOrOldPresc(Convert.ToDateTime(prescdt.Rows[i]["PRESC_DATE"])) == 1)
                                    {
                                        newdt.Rows.Add(prescdt.Rows[i].ItemArray);
                                    }
                                }
                                gc1.DataSource = newdt;

                            }
                        }

                        if (gv1.DataRowCount > idx)
                        {
                            gv1.MoveBy(idx);
                        }
                        else if (gv1.DataRowCount > 0)
                        {
                            gv1.MoveLast();
                        }
                        if (gv1.DataRowCount > 0)
                        {
                            doPass = false;
                            SetBaseInfoAndDetailByPrescDateAndPrescNo(gv1.FocusedRowHandle);
                        }
                        //gv1.DeleteRow(gv1.FocusedRowHandle);

                        txtInput.Focus();
                        if (gv2.DataRowCount > 0 && gv1.DataRowCount > 0 && gv1.FocusedRowHandle >= 0)
                        {
                            bbtnSave.Enabled = true;
                            PrescToQueue();//默认不用
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("\r\nBbtnSave_ItemClick-Exception: " + ex.Message, "温馨提示");
            }

        }
        private DataTable printdt;//打印中药处方用dataset，处理合理用药、卓远医友影响打印中药处方问题
        private void wf_print()
        {
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //DataRow prescdr = masterdr;
            DataRow prescdr = prescdr_p;
            if (prescdr == null) return;
            //if (prescdr["PRESC_SOURCE"].ToString() == "0")//只打印门诊
            //{
            //DataTable detadt = (DataTable)gc2.DataSource;
            DataTable detadt = detadt_p;
            DataRow[] drs = detadt.Select("PRESC_NO='" + prescdr["PRESC_NO"] + "' and PRESC_DATE='" + prescdr["PRESC_DATE"] + "'");
            DataTable newdt = detadt.Clone();
            DataTable dtcs = (DataTable)gc1.DataSource;
            if (drs.Length > 0)
            {
                //txtPrescAttr.Text //毒麻方的处方格式 
                if (txtPrescAttr.Text.Equals("麻醉、精一") || txtPrescAttr.Text.Equals("麻、精一"))
                {
                    StringBuilder sb = new StringBuilder();
                    sb.Append(" select (select to_char(wmsys.wm_concat(o.diagnosis_desc)) from outp_diagnosis o where o.clinic_no=:t3) diagnosis_desc,os.*,cm.name,cm.charge_type,cm.sex,cm.age,pi.unit_in_contract,pi.id_no,dd.dept_name,cm.INSURANCE_NO,decode(os.order_sub_no,1,'','子') zcf,os.DOSAGE||os.DOSAGE_UNITS zyjl,os.DOSAGE||os.DOSAGE_UNITS||'/次   '||decode(pfd.freq_desc_print,null, os.FREQUENCY,pfd.freq_desc_print)  YF,d.toxi_property,os.appoint_no,dd1.dept_name DISPENSARYNAME,'' INP_NO from outp_orders os ,clinic_master cm,PAT_MASTER_INDEX pi, dept_dict dd,perform_freq_dict pfd,drug_dict d,drug_price_list  dpl ,dept_dict dd1 ");
                    sb.Append("  where os.clinic_no = cm.clinic_no ");
                    sb.Append("  and os.patient_id = cm.patient_id");
                    sb.Append("  and os.patient_id = pi.patient_id");
                    sb.Append(" and os.ORDERED_BY = dd.dept_code");
                    sb.Append(" and os.performed_by = dd1.dept_code");
                    sb.Append(" and os.frequency = pfd.freq_desc");
                    sb.Append(" and os.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'");
                    sb.Append(" and os.item_spec = dpl.drug_spec and os.order_code = dpl.drug_code  and os.firm_id = dpl.firm_id and dpl.start_date < os.order_date and ( dpl.stop_date is null or  os.order_date < dpl.stop_date) ");
                    sb.Append(" and d.drug_code = dpl.drug_code   and d.drug_spec = dpl.min_spec ");
                    sb.Append(" and os.clinic_no = :t1 and os.appoint_no = :t2");
                    System.Collections.ArrayList list1 = new System.Collections.ArrayList();
                    List<string> paras1 = new List<string>();
                    paras1.Add("t3");
                    paras1.Add("t1");
                    paras1.Add("t2");
                    list1.Add(dtcs.Rows[0]["CLINIC_NO"].ToString());
                    list1.Add(dtcs.Rows[0]["CLINIC_NO"].ToString());
                    list1.Add(prescdr["PRESC_NO"].ToString());
                    DataSet dsprint = spc.GetDataTable_Para(sb.ToString(), paras1, list1);
                    string sqla = " select sum(costs) from outp_orders os where os.clinic_no = :t1 and os.appoint_no = :t2 and os.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'";
                    System.Collections.ArrayList list2 = new System.Collections.ArrayList();
                    List<string> paras2 = new List<string>();
                    paras2.Add("t1");
                    paras2.Add("t2");
                    list2.Add(dtcs.Rows[0]["CLINIC_NO"].ToString());
                    list2.Add(prescdr["PRESC_NO"].ToString());
                    DataTable dtsum = spc.GetDataTable_Para(sqla.ToString(), paras2, list2).Tables[0];
                    string outpsum = string.Empty;
                    if (dtsum != null && dtsum.Rows.Count > 0)
                    {
                        outpsum = dtsum.Rows[0][0].ToString();
                    }
                    string username = dsprint.Tables[0].Rows[0]["DOCTOR"].ToString();
                    ////报表控件
                    ReportPrintTool tool1;
                    //NursingPlatform.Print_Report.RPT_MZDMF report = new NursingPlatform.Print_Report.RPT_MZDMF(dsprint, outpsum, username,"0"); //0代表门诊 1代表住院
                    RPT_MZDMF report = new RPT_MZDMF(dsprint, outpsum, username, "0");
                    tool1 = new ReportPrintTool(report);
                    report.ShowPrintStatusDialog = false;
                    report.CreateDocument();
                    report.PrintingSystem.ShowMarginsWarning = false;
                    tool1.Print();
                }
                else
                {
                    if (prescdr["PRESC_TYPE"].ToString().Equals("0"))
                    {
                        if (drs.Length <= 5)
                        {
                            foreach (DataRow dr in drs)
                            {
                                newdt.Rows.Add(dr.ItemArray);
                            }
                            RPT_MZQRCF_EL inpprint = new RPT_MZQRCF_EL(prescdr, newdt);
                            ReportPrintTool tool = new ReportPrintTool(inpprint);
                            //tool.PrintingSystem.ShowMarginsWarning = false;
                            //tool.Report.CreateDocument(true);
                            ////tool.ShowPreviewDialog();
                            //tool.Print();
                            inpprint.ShowPrintStatusDialog = false;
                            inpprint.PrintingSystem.ShowMarginsWarning = false;
                            inpprint.CreateDocument();
                            //tool.ShowPreviewDialog();
                            inpprint.Print();
                        }
                        else
                        {
                            double count = Math.Ceiling(Convert.ToDouble(drs.Length) / 5);
                            for (int i = 0; i < count; i++)
                            {
                                newdt.Clear();
                                for (int g = 0; g < 5; g++)
                                {
                                    if (i == 0)
                                    {
                                        newdt.Rows.Add(drs[g].ItemArray);
                                    }
                                    else
                                    {
                                        if ((5 * i + g) < drs.Length)
                                            newdt.Rows.Add(drs[5 * i + g].ItemArray);
                                    }
                                }
                                RPT_MZQRCF_EL inpprint = new RPT_MZQRCF_EL(prescdr, newdt);
                                ReportPrintTool tool = new ReportPrintTool(inpprint);
                                //tool.PrintingSystem.ShowMarginsWarning = false;
                                //tool.Report.CreateDocument(true);                      
                                ////tool.ShowPreviewDialog();
                                //tool.Print();
                                inpprint.ShowPrintStatusDialog = false;
                                inpprint.PrintingSystem.ShowMarginsWarning = false;
                                inpprint.CreateDocument();
                                //tool.ShowPreviewDialog();
                                inpprint.Print();
                            }
                        }
                    }  //西药处方
                    else  //中药处方
                    {
                        foreach (DataRow dr in drs)
                        {
                            newdt.Rows.Add(dr.ItemArray);
                        }
                        printdt = newdt;

                        RPT_MZQRCF_CH inpprint = new RPT_MZQRCF_CH(prescdr, newdt);
                        ReportPrintTool tool = new ReportPrintTool(inpprint);
                        //tool.PrintingSystem.ShowMarginsWarning = false;
                        //tool.Report.CreateDocument(true);
                        ////tool.ShowPreviewDialog();
                        //tool.Print();
                        inpprint.ShowPrintStatusDialog = false;
                        inpprint.PrintingSystem.ShowMarginsWarning = false;
                        inpprint.CreateDocument();
                        //tool.ShowPreviewDialog();
                        inpprint.Print();
                        //新开线程，处理合理用药、卓远医友影响打印中药处方问题 yhy20210709
                        System.Threading.Thread td = new System.Threading.Thread(zyprint);
                        td.Start();
                    }
                }
            }
        }
        void zyprint()
        {
            RPT_MZQRCF_CH inpprint = new RPT_MZQRCF_CH(prescdr_p, printdt);
            ReportPrintTool tool = new ReportPrintTool(inpprint);
            //tool.PrintingSystem.ShowMarginsWarning = false;
            //tool.Report.CreateDocument(true);
            ////tool.ShowPreviewDialog();
            //tool.Print();
            inpprint.ShowPrintStatusDialog = false;
            inpprint.PrintingSystem.ShowMarginsWarning = false;
            inpprint.CreateDocument();
            //tool.ShowPreviewDialog();
            inpprint.Print();

        }
        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnrefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //System.Threading.Thread myThread = new System.Threading.Thread(InitData); //20210709 yhy
            //myThread.Start();
            // FrmOutpOnePresdispDeliver_Load(null, null);
            rg1.SelectedIndex = 2;
            InitData();
            //txtBarcode.Focus();
            rg1.SelectedIndex = 0;
            is_seatcode = is_windws_no;

            if (canshu.Equals("1"))
            {
                //合理用药初始化
                Load_State = PlatPublic.Common.MeiKang_Transfer.MeiKang_Load("mzyf", PlatCommon.SysBase.SystemParm.HospitalID, PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME);
            }

            if (string.IsNullOrEmpty(is_text))
            {
                is_text = messageinfo.Text;
            }
            init_queue_call();
            gv1.Focus();

            cmbType.Text = "病人ID";
            txtInput.Text = "";
            //CleanTxt();
            //bbtnSave.Enabled = false;
            if (gv1.RowCount > 0)
            {
                doPass = false;
                SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
            }

        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bbtnExit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void bbtnM1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                //20240801老的改成新的
                //////bbtnrefresh_ItemClick(null, null);
                //////IdentityCardMessage idcm = new IdentityCardMessage();//读卡返回信息
                //////string identityCardType = SystemParm.GetParameterValue("READ_CARD_NO_TYPE", this.AppCode, "*", "*", SystemParm.HisUnitCode);
                //////if (ReadIdentityCardPblic.ReadHospitalCard(identityCardType, ref idcm) != 1) return;//读取M1卡
                //////string ls_cardno = idcm.IdCardno;
                //////Comm.ucc2_his33_card_manager luo_card = new Comm.ucc2_his33_card_manager();
                //////string ls_patient_id = "";
                //////if (luo_card.uf_check_valid(ref ls_patient_id, ls_cardno, false) != 0) return;
                //////cmbType.EditValue = "病人ID";
                //////txtInput.EditValue = ls_patient_id;
                //////KeyEventArgs kea = new KeyEventArgs(Keys.Enter);
                //////txtInput_KeyDown(null, kea);
                //20240801
                //txtInput.Focus();
                //SendKeys.Send("{ENTER}");
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.ToString(), "提示", MessageBoxButtons.OK);
            }

        }

        private void bbtnCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                //20240801老的方式
                //bbtnrefresh_ItemClick(null, null);
                //IdentityCardMessage idcm = new IdentityCardMessage();//读卡返回信息
                //string ls_patient_id;
                //string ls_pName, ls_pSex, ls_pNation, ls_pBirth, ls_pAddress, ls_pCertNo;
                //string identityCardType = SystemParm.GetParameterValue("READ_ID_NO_TYPE", this.AppCode, "*", "*", SystemParm.HisUnitCode);
                //if (ReadIdentityCardPblic.ReadIdentityCard(identityCardType, ref idcm) != 1) return;//读身份证
                //ls_pName = idcm.Name;
                //ls_pSex = idcm.Sex;
                //ls_pNation = idcm.Nation;
                //ls_pBirth = idcm.Born;
                //ls_pAddress = idcm.Address;
                //ls_pCertNo = idcm.IdCardno;
                //string sql = "SELECT PATIENT_ID FROM PAT_MASTER_INDEX WHERE ID_NO='" + ls_pCertNo + "'";
                //ls_patient_id = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
                //if (string.IsNullOrEmpty(ls_patient_id))
                //{
                //    XtraMessageBox.Show("没有找到身份证信息！", "提示", MessageBoxButtons.OK);
                //    return;
                //}
                //cmbType.EditValue = "病人ID";
                //txtInput.EditValue = ls_patient_id;
                //KeyEventArgs kea = new KeyEventArgs(Keys.Enter);
                //txtInput_KeyDown(null, kea);
                //20240801老的方式



            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.ToString(), "提示", MessageBoxButtons.OK);
            }
        }

        /// <summary>
        /// 单选框选中变化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void rg1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (prescdt.Rows.Count <= 0) return;

            DateTimeSYSDATE = GetSysTime();
            if (rg1.SelectedIndex == 2)
            {
                prescdt.DefaultView.Sort = "PRESC_DATE desc";
                gc1.DataSource = prescdt;
                gv1.Focus();
                gv1.FocusedRowHandle = 0;
                //bbtnSave.Enabled = false;
            }
            else if (rg1.SelectedIndex == 1)//过期处方
            {
                DataTable olddt = prescdt.Clone();
                for (int i = 0; i < prescdt.Rows.Count; i++)
                {
                    if (SetNewOrOldPresc(Convert.ToDateTime(prescdt.Rows[i]["PRESC_DATE"])) == 0)
                    {
                        olddt.Rows.Add(prescdt.Rows[i].ItemArray);
                    }
                }
                olddt.DefaultView.Sort = "PRESC_DATE desc";
                gc1.DataSource = olddt;
                gv1.Focus();
                gv1.FocusedRowHandle = 0;
                //bbtnSave.Enabled = false;
            }
            else if (rg1.SelectedIndex == 0)//当前处方
            {
                DataTable newdt = prescdt.Clone();
                for (int i = 0; i < prescdt.Rows.Count; i++)
                {
                    if (SetNewOrOldPresc(Convert.ToDateTime(prescdt.Rows[i]["PRESC_DATE"])) == 1)
                    {
                        newdt.Rows.Add(prescdt.Rows[i].ItemArray);
                    }
                }
                gc1.DataSource = newdt;
                gv1.Focus();
                gv1.FocusedRowHandle = 0;
                //bbtnSave.Enabled = false;
            }
            if (gv1.RowCount > 0)
            {
                doPass = false;
                SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
            }
        }

        private void gv1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            if (e.RowHandle < 0) return;
            SetBaseInfoAndDetailByPrescDateAndPrescNo(e.RowHandle);
            if (gv1.FocusedRowHandle < 0 || gv1.DataRowCount == 0)
            {
                clearPresc();
                return;
            }
            doPass = true;
            SetBaseInfoAndDetailByPrescDateAndPrescNo(gv1.FocusedRowHandle);
        }

        /// <summary>
        /// 双击左侧处方号，确定按钮可以用
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv1_DoubleClick(object sender, EventArgs e)
        {
            if (gv1.GetSelectedRows().Length < 1) return;
            string prescno = gv1.GetRowCellValue(gv1.GetSelectedRows()[0], "PRESC_NO").ToString();
            DataRow[] drs = prescdt.Select("PRESC_NO='" + prescno + "'");
            if (drs.Length > 0 && allow_confirm == true)
            {
                DataTable newdt = new DataTable();
                newdt = prescdt.Clone();
                newdt.Rows.Add(drs[0].ItemArray);
                gc1.DataSource = newdt;
                bbtnSave.Enabled = true;
                SetBaseInfoAndDetailByPrescDateAndPrescNo(gv1.GetSelectedRows()[0]);
                PrescToQueue();
            }
        }

        private byte[] ImageToByteArray(Image image)
        {
            System.IO.MemoryStream mStream = new System.IO.MemoryStream();
            image.Save(mStream, System.Drawing.Imaging.ImageFormat.Png);
            byte[] ret = mStream.ToArray();
            mStream.Close();
            return ret;
        }

        /// <summary>
        /// 单击明细，显示价钱和库存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void gv2_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            DataTable drugdt = (DataTable)gc2.DataSource;
            if (drugdt.Rows.Count > 0)
            {
                //显示零售价和库存
                lbStockCount.Text = GetDrugStockByDrugCode(this.DeptCode, drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString(), drugdt.Rows[e.RowHandle]["PACKAGE_SPEC"].ToString()).Rows[0][0].ToString();
                DataTable drugprice = GetDrugPriceDrugCode(drugdt.Rows[e.RowHandle]["DRUG_CODE"].ToString(), drugdt.Rows[e.RowHandle]["PACKAGE_SPEC"].ToString(), drugdt.Rows[e.RowHandle]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
            }
            else
            {
                lbStockCount.Text = "";
                lbPrice.Text = "";
            }
        }

        /// <summary>
        /// 输入框检索病人
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void txtInput_KeyDown(object sender, KeyEventArgs e)
        {
            Keys key = e.KeyCode;
            if (key == Keys.Enter)
            {
                if (txtInput.Text.Trim().Length < 1) return;
                //DataTable dt = (DataTable)gc1.DataSource;
                DataTable dt = prescdt;
                if (dt.Rows.Count > 0)
                {
                    DataRow[] drs;
                    if (cmbType.Text == "病人ID")
                    {
                        drs = dt.Select("PATIENT_ID='" + txtInput.Text.Trim().ToUpper() + "'");
                    }
                    else if (cmbType.Text == "处方号")
                    {
                        drs = dt.Select("PRESC_NO='" + txtInput.Text.Trim() + "'");
                    }
                    else if (cmbType.Text == "拼音")
                    {
                        drs = dt.Select("NAME_PHONETIC='" + txtInput.Text.Trim() + "'");
                    }
                    else if (cmbType.Text == "姓名")
                    {
                        drs = dt.Select("NAME='" + txtInput.Text.Trim() + "'");
                    }
                    else if (cmbType.Text == "收据号")
                    {
                        drs = dt.Select("RCPT_NO='" + txtInput.Text.Trim() + "'");
                    }
                    else if (cmbType.Text == "身份证")
                    {
                        string patientId = "";
                        string visitId = "";
                        if (!string.IsNullOrEmpty(txtInput.Text.Trim()))
                        {
                            if (PlatCommon.Common.PublicFunction.GetPatientIdForIdNo(ref patientId, ref visitId, txtInput.Text.Trim()))
                            {
                            }
                            else
                            {
                                XtraMessageBox.Show("取数据失败", "提示");
                                return;
                            }
                            drs = dt.Select("PATIENT_ID='" + patientId + "'");
                        }
                        else
                        {
                            drs = null;
                        }

                    }
                    else
                    {
                        drs = dt.Select("PATIENT_ID=''");
                    }
                    if (drs.Length == 0)
                    {
                        XtraMessageBox.Show("不存在对应的记录！");
                        return;
                    }
                    else
                    {
                        DataTable newdt = dt.Clone();
                        for (int i = 0; i < drs.Length; i++)
                        {
                            newdt.Rows.Add(drs[i].ItemArray);
                        }
                        gc1.DataSource = newdt;
                        gv1.FocusedRowHandle = 0;
                        bbtnSave.Enabled = true;

                        doPass = true;
                        SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
                    }
                }
                //txtBarcode.Focus();
            }
            if (key == Keys.Space)
            {
                DataTable dt = (DataTable)gc1.DataSource;
                if (dt.Rows.Count > 0)
                {
                    if (cmbType.Text == "药品名称")
                    {
                        string presc_no = "";
                        foreach (DataRow dr in dt.Rows)
                        {
                            presc_no += dr["PRESC_NO"] + ",";
                        }
                        if (presc_no.Length > 0)
                        {
                            presc_no = presc_no.Substring(0, presc_no.Length - 1);
                        }
                        string drug_code = "";
                        using (PlatCommon.Common.frminputsettingoutp frm = new PlatCommon.Common.frminputsettingoutp())
                        {
                            frm.GS_INPUTSETING = "诊疗药品_合并";
                            frm.GS_ITEM_CLASS = "A";
                            frm.GS_DISP = this.DeptCode;//药局传进去
                            frm.ShowDialog();
                            if (frm.GS_ITEM_CODE == null) return;
                            drug_code = frm.GS_ITEM_CODE;
                            txtInput.Text = frm.GS_ITEM_NAME;
                        }
                        string sql = "SELECT a.presc_date,a.presc_no,a.patient_id,b.drug_code FROM DRUG_PRESC_MASTER_TEMP A,drug_presc_detail_temp b WHERE A.PRESC_DATE=B.PRESC_DATE AND A.PRESC_NO=B.PRESC_NO AND A.PRESC_NO IN (" + presc_no + ") AND B.DRUG_CODE='" + drug_code + "'";
                        DataTable dt_filter = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sql).Tables[0];
                        presc_no = string.Empty;
                        foreach (DataRow dr in dt_filter.Rows)
                        {
                            presc_no += dr["PRESC_NO"] + ",";
                        }
                        if (presc_no.Length > 0)
                        {
                            presc_no = presc_no.Substring(0, presc_no.Length - 1);
                        }
                        DataRow[] drs;
                        if (string.IsNullOrEmpty(presc_no)) drs = dt.Select("PRESC_NO is null");
                        else drs = dt.Select("PRESC_NO in (" + presc_no + ")");
                        if (drs.Length == 0)
                        {
                            XtraMessageBox.Show("不存在对应的记录！");
                            return;
                        }
                        else
                        {
                            DataTable newdt = dt.Clone();
                            for (int i = 0; i < drs.Length; i++)
                            {
                                newdt.Rows.Add(drs[i].ItemArray);
                            }
                            gc1.DataSource = newdt;
                            doPass = true;
                            SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 叫号启用
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_start_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            cb_start_Click();
        }

        private void cb_start_Click()
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();
            //if (ClassPrescQueue.uf_set_client_status(SystemParm.Deptcode, is_seatcode, ClassPrescQueue.CLIENT_STATUS_INSERVICE, ref idc) > 0)
            //{

            //}
            string li_min;
            //取发药药局最小的值
            DataTable dt = new ServerPublicClient().GetDataBySql("select nvl(min(num_no),0) num_no from DRUG_WINDOWS_NO_DICT where drug_code ='" + this.DeptCode + "' and type_code =1").Tables[0];
            li_min = dt.Rows[0][0].ToString();
            string sql = "update DRUG_WINDOWS_NO_DICT set  type_code=1,num_no =" + li_min + " where drug_code='" + this.DeptCode + "' and windows_no='" + is_windws_no + "'";
            idc.Add(sql, "更新窗口分配患者数失败！");
            st_client_status = Comm.ClassPrescQueue.CLIENT_STATUS_INSERVICE_STRING;
            string result = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
                cb_start.Enabled = false;
                cb_pause.Enabled = true;
                cb_stop.Enabled = true;
            }
            messageinfo.Text = is_text + "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
        }
        /// <summary>
        /// 叫号停排
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_pause_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            cb_pause_Click();
        }
        private void cb_pause_Click()
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();
            //if (ClassPrescQueue.uf_set_client_status(SystemParm.Deptcode, is_seatcode, ClassPrescQueue.CLIENT_STATUS_PAUSE, ref idc) > 0)
            //{
            st_client_status = Comm.ClassPrescQueue.CLIENT_STATUS_PAUSE_STRING;
            //}
            //else
            //{
            //    return;
            //}
            string sql = "update DRUG_WINDOWS_NO_DICT set type_code=0 where drug_code='" + this.DeptCode + "' and windows_no='" + is_windws_no + "'";
            idc.Add(sql, "停排窗口失败！窗口号:" + is_seatcode);

            string result = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
                cb_start.Enabled = true;
                cb_pause.Enabled = false;
            }
            st_queue_status = Comm.ClassPrescQueue.MSG_IDLE;
            messageinfo.Text = is_text + "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
            wf_disp_waitdispense();
        }
        /// <summary>
        /// 叫号停用
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_stop_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            cb_stop_Click();
        }
        private void cb_stop_Click()
        {
            //cb_stop_Click(); //先调用停排 再查询是否还有患者 有的话转移到其他窗口取药
            cb_pause_Click();//先调用停排 再查询是否还有患者 有的话转移到其他窗口取药
            if (Comm.ClassPrescQueue.uf_set_client_status(this.DeptCode, is_seatcode, Comm.ClassPrescQueue.CLIENT_STATUS_CLOSED) > 0)
            {
                st_client_status = Comm.ClassPrescQueue.CLIENT_STATUS_CLOSED_STRING;
                il_id = 0;
            }
            else
            {
                return;
            }
            st_queue_status = Comm.ClassPrescQueue.MSG_IDLE;

            wf_disp_waitdispense();



            cb_start.Enabled = true;
            cb_stop.Enabled = false;
            messageinfo.Text = is_text + "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
        }
        /// <summary>
        /// 叫号摆药
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_prepare_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            cb_prepare_Click();
        }
        private void cb_prepare_Click()
        {
            string ls_presc_date = "", ls_presc_no = "", ls_name = "";
            int ll_id;
            if (dt_queue_wait.Rows.Count <= 0)
            {
                st_queue_wait = "待摆:" + "0" + "人";
                messageinfo.Text = "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
                return;
            }
            ll_id = Comm.ClassPrescQueue.uf_client_get_main(this.DeptCode, is_seatcode, Comm.ClassPrescQueue.STATUS_WAIT_PREPARE);
            if (ll_id <= 0) return;
            if (Comm.ClassPrescQueue.uf_get_presc_byid(ll_id, ref ls_presc_date, ref ls_presc_no, ref ls_name) > 0)
            {
                st_queue_status = ls_name;
            }

            //开始摆药
            Comm.ClassPrescQueue.uf_set_status(ll_id, Comm.ClassPrescQueue.STATUS_PREPARING);
            //摆药完成，等待发药
            Comm.ClassPrescQueue.uf_set_status(ll_id, Comm.ClassPrescQueue.STATUS_WAIT_DISPENSE);

            if (cbx_audio_auto.EditValue.Equals("1"))
            {
                Comm.ClassPrescQueue.uf_set_noticeflag(ll_id, 1);
            }
            //打印用于摆药的处方 暂留
            wf_disp_waitdispense();
            wf_disp_waitprepare();

            messageinfo.Text = is_text + "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
        }
        /// <summary>
        /// 叫号呼叫
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_audio_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();
            int ll_handle;
            ll_handle = gv1.FocusedRowHandle;
            if (ll_handle < 0) return;
            DataTable dt = gc1.DataSource as DataTable;
            string ls_patient_id = dt.Rows[ll_handle]["PATIENT_ID"].ToString();
            string ls_name = dt.Rows[ll_handle]["NAME"].ToString();
            int ll_presc_widno = Convert.ToInt32(dt.Rows[ll_handle]["PRESC_WIDNO"]);
            string sql = "select count(*) into :ll_count from wisedisplay.pdjh_officina_que_mid@dblink_hf t where t.dept_code ='" + this.DeptCode + "' and t.window_id =" + ll_presc_widno + "";
            int ll_count = int.Parse(new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql));
            if (ll_count == 0)
            {
                XtraMessageBox.Show("未找到呼叫窗口号!", "提示", MessageBoxButtons.OK);
                return;
            }
            sql = "update wisedisplay.pdjh_officina_que_mid@dblink_hf set iscalled = 1, up_time = sysdate, patient_id ='" + ls_patient_id + "',name ='" + ls_name + "',display_led = 1 where dept_code ='" + this.DeptCode + "' and window_id =" + ll_presc_widno + "";
            idc.Add(sql, "更新呼叫状态");
            string result = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                XtraMessageBox.Show(result, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
                //启动定时器
                timer1.Interval = 2000;
                timer1.Start();
                XtraMessageBox.Show("叫号成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            // 停止定时器
            timer1.Stop();
            // 向对话框发送按键 Enter
            SendKeys.Send("ENTER");
        }
        /// <summary>
        /// 叫号过号
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cb_pass_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();
            string ldt_presc_date, ll_presc_no;
            int ll_handle;
            ll_handle = gv1.FocusedRowHandle;
            if (ll_handle < 0) return;
            ldt_presc_date = gv1.GetRowCellValue(ll_handle, "PRESC_DATE").ToString();
            ll_presc_no = gv1.GetRowCellValue(ll_handle, "PRESC_NO").ToString();
            string sql = "update DRUG_PRESC_QUEUE set  status=8 where presc_Date =to_date('" + ldt_presc_date + "','yyyy-mm-dd hh24:mi:ss') and presc_no=" + ll_presc_no + "";
            idc.Add(sql, "更新过号状态");
            string result = new NM_Service.NMService.ServerPublicClient().SaveTable(idc);
            if (!string.IsNullOrEmpty(result))
            {
                XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            else
            {
            }
            wf_disp_waitdispense();
        }
        /// <summary>
        /// 窗口选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void rg_windws_check_SelectedIndexChanged(object sender, EventArgs e)
        {
            FrmOutpOnePresdispDeliver_Load(null, null);
            if (gv1.RowCount > 0)
            {
                doPass = false;
                SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
            }
        }
        #endregion

        #region 方法
        /// <summary>
        /// 初始化右侧处方列表
        /// </summary>
        private void InitData()
        {
            string chargeflag = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGE_FLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string presc_verified = PlatCommon.SysBase.SystemParm.GetParameterValue("PRESC_VERIFIED", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            string chargetypes = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGETYPES", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
            DateTime starttime = DateTime.Now.AddDays(-showPrescDays);
            DateTime endtime = DateTime.Now.AddDays(1);

            if (chargeflag == "1")
            {
                if (presc_verified == "1")
                {
                    prescdt = GetDrugPrescMasterTempByChargeFlag1Verified1(starttime, endtime, this.DeptCode, chargetypes);
                }
                else
                {
                    prescdt = GetDrugPrescMasterTempByChargeFlag1Verified0(starttime, endtime, this.DeptCode, chargetypes);
                }
            }
            else
            {
                if (presc_verified == "1")
                {
                    prescdt = GetDrugPrescMasterTempByChargeFlag0Verified1(starttime, endtime, this.DeptCode);
                }
                else
                {
                    prescdt = GetDrugPrescMasterTempByChargeFlag0Verified0(starttime, endtime, this.DeptCode);
                }
            }
            gc1.DataSource = prescdt;

            // 检测新处方并播放语音提示
            CheckNewPrescAndPlayVoice(prescdt);

            if (prescdt.Rows.Count > 0)
            {
                //SetBaseInfoAndDetailByPrescDateAndPrescNo(0);
            }
            else
            {
                CleanTxt();
            }
        }
        /// <summary>
        /// 判断当前处方过期处方
        /// </summary>
        /// <param name="prescdate"></param>
        /// <returns>1当前处方0过期处方</returns>
        private int SetNewOrOldPresc(DateTime prescdate)
        {

            if (prescdate.ToString("yyyy-MM-dd") != DateTimeSYSDATE.ToString("yyyy-MM-dd"))
                return 0; //过期处方
            else
                return 1;
            //if (sysdate.Hour <= first && prescdate.Hour <= first) return 1;
            //if (sysdate.Hour <= first && prescdate.Hour > first) return 0;
            //if (sysdate.Hour > first && sysdate.Hour < mid)
            //{
            //    if (prescdate.Hour <= first) return 0;
            //    else if (prescdate.Hour > first && prescdate.Hour <= mid) return 1;
            //}
            //if (sysdate.Hour > mid)
            //{
            //    if (prescdate.Hour <= mid) return 0;
            //    else if (prescdate.Hour > mid) return 1;
            //}
            //return 0;
        }
        /// <summary>
        /// 计算年龄
        /// </summary>
        /// <param name="beginDateTime">出生年月</param>
        /// <param name="endDateTime">当前日期</param>
        public static string CalculationDate(DateTime beginDateTime, DateTime endDateTime)
        {
            string birth = "";
            if (beginDateTime > endDateTime)
                throw new Exception("出生日期应小于或等与当前日期！");

            /*计算出生日期到当前日期总月数*/

            int Months = endDateTime.Month - beginDateTime.Month + 12 * (endDateTime.Year - beginDateTime.Year);

            /*出生日期加总月数后，如果大于当前日期则减一个月*/

            int totalMonth = (beginDateTime.AddMonths(Months) > endDateTime) ? Months - 1 : Months;

            /*计算整年*/

            int fullYear = totalMonth / 12;

            /*计算整月*/

            int fullMonth = totalMonth % 12;

            /*计算天数*/

            DateTime changeDate = beginDateTime.AddMonths(totalMonth);

            double days = (endDateTime - changeDate).TotalDays;

            if (fullYear <= 0)
            {
                if (fullMonth <= 0)
                {
                    birth = (((int)days).ToString()).ToString() + "天";
                }
                else
                {
                    birth = fullMonth.ToString() + "月" + (((int)days).ToString()).ToString() + "天";
                }
            }
            else
            {
                birth = fullYear.ToString() + "岁";
            }
            return birth;

        }

        /// <summary>
        /// 根据处方号和处方日期显示处方信息
        /// </summary>
        /// <param name="rowindex"></param>
        private void SetBaseInfoAndDetailByPrescDateAndPrescNo(int rowindex)
        {
            string prescdate = gv1.GetRowCellValue(rowindex, "PRESC_DATE").ToString();
            string prescno = gv1.GetRowCellValue(rowindex, "PRESC_NO").ToString();

            //获取待发药主记录
            DataTable masterdt = GetPrescMasterTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);
            // 
            if (masterdt.Rows.Count > 0)
            {
                txtPatientID.Text = masterdt.Rows[0]["PATIENT_ID"].ToString();
                txtName.Text = masterdt.Rows[0]["NAME"].ToString();

                txtAge.Text = masterdt.Rows[0]["DATE_OF_BIRTH"].Equals(DBNull.Value) ? "" : CalculationDate(DateTime.Parse(masterdt.Rows[0]["DATE_OF_BIRTH"].ToString()), sysDateTime);
                //if (masterdt.Rows[0]["DATE_OF_BIRTH"].Equals(DBNull.Value))
                //{
                //    txtAge.Text = "";
                //}
                //else
                //{
                //    DateTime birthDay = DateTime.Parse(masterdt.Rows[0]["DATE_OF_BIRTH"].ToString());
                //    txtAge.Text = CalculationDate(birthDay, sysDateTime);
                //}
                txtBirth.Text = masterdt.Rows[0]["AGE"].ToString();
                txtSex.Text = masterdt.Rows[0]["SEX"].ToString();
                txtIdentity.Text = masterdt.Rows[0]["IDENTITY"].ToString();
                txtChargeType.Text = masterdt.Rows[0]["CHARGE_TYPE"].ToString();
                txtPrescAttr.Text = masterdt.Rows[0]["PRESC_ATTR"].ToString();
                txtPrescDate.Text = Convert.ToDateTime(masterdt.Rows[0]["PRESC_DATE"].ToString()).ToString("yyyy-MM-dd");
                txtUnitincontract.Text = masterdt.Rows[0]["UNIT_IN_CONTRACT"].ToString();
                txtPrescribed.Text = masterdt.Rows[0]["PRESCRIBED_BY"].ToString();
                txtPrescSource.Text = masterdt.Rows[0]["PRESC_SOURCE"].ToString() == "0" ? "门诊处方" : "住院处方";
                txtOrderedby.Text = masterdt.Rows[0]["DEPT_NAME"].ToString();
                txtEnteredby.Text = masterdt.Rows[0]["ENTERED_NAME"].ToString();
                txtRepetition.Text = masterdt.Rows[0]["REPETITION"].ToString();
                txtCountRepetition.Text = masterdt.Rows[0]["COUNT_PER_REPETITION"].ToString();
                txtdiagnosis.Text = masterdt.Rows[0]["DIAG_DESC"].ToString();
                //2022-5-18 OUTP_DIAGNOSIS表CLINIC_NO都为空
                string sqlzd = @"SELECT T.DIAGNOSIS_DESC FROM OUTP_DIAGNOSIS T WHERE T.CLINIC_NO='" + masterdt.Rows[0]["CLINIC_NO"].ToString() + @"' AND ROWNUM=1 ORDER BY T.ORDINAL,T.DIAGNOSIS_NO,T.DIAGNOSIS_INDICATOR ASC ";
                DataTable dtzd = new NM_Service.NMService.ServerPublicClient().GetDataBySql(sqlzd).Tables[0];
                if (dtzd != null && dtzd.Rows.Count > 0)
                {
                    //txtdiagnosis.Text = dtzd.Rows[0][0].ToString();
                }
                if (masterdt.Rows[0]["DECOCTION"] == DBNull.Value)
                {
                    txtDecoction.Text = "";
                }
                else
                {
                    // 根据DECOCTION字段值显示对应的代煎状态
                    string decoctionValue = masterdt.Rows[0]["DECOCTION"].ToString();
                    switch (decoctionValue)
                    {
                        case "0":
                            txtDecoction.Text = "不代煎";
                            break;
                        case "1":
                            txtDecoction.Text = "人工代煎";
                            break;
                        case "2":
                            txtDecoction.Text = "机器代煎";
                            break;
                        default:
                            txtDecoction.Text = "不代煎";
                            break;
                    }
                }
                labelControl7.Text = masterdt.Rows[0]["DATE_OF_BIRTH"].ToString();
                txtBloodPressure.Text = "";
                txtTemperature.Text = "";
                txtHeight.Text = "";
                txtWeight.Text = "";
                string bloodPressure = "";//血压
                string temperature = "";//体温
                string height = "";//身高
                string weight = "";//体重
                DataRow dr = Comm.GetInpNrTempSignsRec.getOutpVitalSignsCvalue(masterdt.Rows[0]["CLINIC_NO"].ToString());
                if (dr != null)
                {
                    height = dr["HEIGHT"].ToString();
                    weight = dr["WEIGHT"].ToString();
                    bloodPressure = dr["BLOODPRESSURE"].ToString();
                    temperature = dr["TEMPERATURE"].ToString();
                    txtBloodPressure.Text = bloodPressure;
                    txtTemperature.Text = temperature;
                    txtHeight.Text = height;
                    txtWeight.Text = weight;
                }

                masterdr = masterdt.Rows[0];

                //美康合理用药
                if (canshu.Equals("1") && Load_State == 1 && doPass)
                {
                    //传入患者基本信息
                    PlatPublic.Common.MeiKang_Transfer.MDC_SetPatient(masterdt.Rows[0]["PATIENT_ID"].ToString(), masterdt.Rows[0]["CLINIC_NO"].ToString());
                    //传入诊断信息
                    PlatPublic.Common.MeiKang_Transfer.MDC_AddMedCond(masterdt.Rows[0]["PATIENT_ID"].ToString(), masterdt.Rows[0]["CLINIC_NO"].ToString());
                }
            }
            else
            {
                masterdr = null;
            }

            //获取待发药明细记录20220428
            // XtraMessageBox.Show("GetPrescDetailTempByDateAndNo", "温馨提示1");
            DataTable detaildt = GetPrescDetailTempByDateAndNo(Convert.ToDateTime(prescdate), prescno);
            if (detaildt.Rows.Count > 0)
            {
                foreach (DataRow drds in detaildt.Rows)
                {
                    DataTable dtds = new DataTable();
                    GetDrugStock(this.DeptCode, drds["DRUG_CODE"].ToString(), drds["DRUG_SPEC"].ToString(),
                        drds["PACKAGE_SPEC"].ToString(), drds["FIRM_ID"].ToString(), drds["BATCH_NO"].ToString(), drds["BATCH_CODE"].ToString(), drds["RETAIL_PRICE"].ToString(), ref dtds);
                    if (dtds.Rows.Count > 0)
                    {
                        if (!string.IsNullOrEmpty(dtds.Rows[0]["BATCH_NO"].ToString()))
                        {
                            drds["BATCH_NO"] = dtds.Rows[0]["BATCH_NO"].ToString();
                        }
                        if (!string.IsNullOrEmpty(dtds.Rows[0]["BATCH_CODE"].ToString()))
                        {
                            drds["BATCH_CODE"] = dtds.Rows[0]["BATCH_CODE"].ToString();
                        }
                    }
                }
            }
            gc2.DataSource = detaildt;
            gv2.ClearSelection();
            if (detaildt.Rows.Count > 0)
            {
                gv2.BestFitColumns();
                //显示零售价和库存
                lbStockCount.Text = GetDrugStockByDrugCode(this.DeptCode, detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString(), detaildt.Rows[0]["PACKAGE_SPEC"].ToString()).Rows[0][0].ToString();
                DataTable drugprice = GetDrugPriceDrugCode(detaildt.Rows[0]["DRUG_CODE"].ToString(), detaildt.Rows[0]["PACKAGE_SPEC"].ToString(), detaildt.Rows[0]["FIRM_ID"].ToString());
                if (drugprice.Rows.Count > 0)
                {
                    lbPrice.Text = drugprice.Rows[0]["retail_price"] != DBNull.Value ? drugprice.Rows[0]["retail_price"].ToString() : "";
                }
                else
                {
                    lbPrice.Text = "";
                }
            }
            else
            {
                lbStockCount.Text = "";
                lbPrice.Text = "";
            }
            if (masterdt.Rows.Count > 0)
            {
                int result = SetNewOrOldPresc(Convert.ToDateTime(masterdt.Rows[0]["PRESC_DATE"]));
                labelControl6.Text = result == 1 ? "此处方为当前处方!" : "此处方已过期!";
            }

            DataTable freqdt = new DataTable();
            DataTable dosagedt = new DataTable();

            //判断每条处方明细正确性
            for (int i = 0; i < detaildt.Rows.Count; i++)
            {
                try
                {
                    //美康合理用药
                    if (canshu.Equals("1") && Load_State == 1 && doPass)
                    {
                        string pcIndex = masterdt.Rows[0]["CLINIC_NO"].ToString() + '_' + detaildt.Rows[i]["PRESC_NO"].ToString() + '_' + detaildt.Rows[i]["ITEM_NO"].ToString(); //主键
                        int piOrderNo = int.Parse(detaildt.Rows[i]["ORDER_NO"] == DBNull.Value ? "0" : detaildt.Rows[i]["ORDER_NO"].ToString().Trim()); //医嘱号                                                                                                                                                  
                        string pcDrugUniqueCode = detaildt.Rows[i]["DRUG_CODE"].ToString(); //药品代码
                        string pcDrugName = detaildt.Rows[i]["DRUG_NAME"].ToString(); //药名
                        string pcDosePerTime = detaildt.Rows[i]["DOSAGE_EACH"].ToString(); //用量
                        string pcDoseUnit = detaildt.Rows[i]["DOSAGE_UNITS"].ToString(); //用量单位
                        string pcFrequency = detaildt.Rows[i]["ADMINISTRATION"].ToString().Split(';')[3]; //频次
                        string pcRouteCode = detaildt.Rows[i]["ADMINISTRATION"].ToString().Split(';')[2]; //途径编码
                        string pcRouteName = detaildt.Rows[i]["ADMINISTRATION"].ToString().Split(';')[2]; //途径名称
                        string pcStartTime = Convert.ToDateTime(detaildt.Rows[i]["PRESC_DATE"]).ToString("yyyy-MM-dd HH:mm:ss"); //开医嘱时间
                        string pcEndTime = pcStartTime; //停嘱时间
                        string pcExecuteTime = pcStartTime; //执行时间
                        string pcGroupTag = piOrderNo.ToString(); //成组医嘱标记
                        string pcIsTempDrug = "1"; //医嘱类型
                        string pcOrderType = "0"; //在用（默认）
                        string pcDeptCode = masterdt.Rows[0]["ORDERED_BY"].ToString(); //科室编码
                        string sql = " select dept_name from dept_dict where dept_code = '" + pcDeptCode + "' ";
                        string pcDeptName = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql); //科室名称
                        string pcDoctorCode = masterdt.Rows[0]["PRESCRIBED_USERCODE"].ToString(); //医生编码
                        string pcDoctorName = masterdt.Rows[0]["PRESCRIBED_BY"].ToString(); //医生名称
                        string pcRecipNo = masterdt.Rows[0]["PRESC_NO"].ToString(); //处方号
                        string pcNum = detaildt.Rows[i]["QUANTITY"].ToString(); //药品数量
                        string pcNumUnit = detaildt.Rows[i]["PACKAGE_UNITS"].ToString(); //单位
                        string pcPurpose = "0"; //用药目的
                        string pcOprCode = ""; //手术编号
                        string pcMediTime = "0"; //手术用药时机类型
                        string pcRemark = ""; //医嘱备注信息

                        //传入药品信息
                        PlatPublic.Common.MeiKang_Transfer.MDC_AddScreenDrug(pcIndex, piOrderNo, pcDrugUniqueCode, pcDrugName, pcDosePerTime,
                            pcDoseUnit, pcFrequency, pcRouteCode, pcRouteName, pcStartTime, pcEndTime,
                            pcExecuteTime, pcGroupTag, pcIsTempDrug, pcOrderType, pcDeptCode, pcDeptName, pcDoctorCode,
                            pcDoctorName, pcRecipNo, pcNum, pcNumUnit, pcPurpose, pcOprCode, pcMediTime, pcRemark);
                    }

                }
                catch
                {
                    continue;
                }
            }

            //美康合理用药
            if (canshu.Equals("1") && Load_State == 1 && doPass)
            {
                //审核用药
                PlatPublic.Common.MeiKang_Transfer.DoCheck(false);
            }
        }

        //
        private void clearPresc()
        {
            txtPatientID.Text = "";
            txtName.Text = "";
            txtAge.Text = "";
            txtSex.Text = "";
            txtIdentity.Text = "";
            txtChargeType.Text = "";
            txtPrescAttr.Text = "";
            txtPrescDate.Text = "";
            txtUnitincontract.Text = "";
            txtPrescribed.Text = "";
            txtPrescSource.Text = "";
            txtOrderedby.Text = "";
            txtEnteredby.Text = "";
            txtRepetition.Text = "";
            txtCountRepetition.Text = "";
            txtDecoction.Text = "";
            labelControl7.Text = "";

            gc2.DataSource = null;
        }

        /// <summary>
        /// 发药机接口
        /// </summary>
        private void PrescToQueue()
        {
            if (prescqueue == "1")
            {
                Dictionary<string, string> allsave = new Dictionary<string, string>();
                string sqlstr = UpdateDrugPrescQueue(gv1.GetRowCellValue(gv1.GetSelectedRows()[0], "PRESC_DATE").ToString(), gv1.GetRowCellValue(gv1.GetSelectedRows()[0], "PRESC_NO").ToString());
                if (!allsave.ContainsKey(sqlstr))
                    allsave.Add(sqlstr, "修改发药状态失败！");
                if (allsave.Count > 0)
                {
                    string result = new ServerPublicClient().SaveTable(allsave);
                    if (result.Length > 0)
                    {
                        XtraMessageBox.Show(result);
                    }
                    else
                    {
                        //return "";
                    }
                }
            }
        }
        /// <summary>
        /// 清理文本框
        /// </summary>
        private void CleanTxt()
        {
            txtPatientID.Text = "";
            txtName.Text = "";
            txtAge.Text = "";
            txtSex.Text = "";
            txtIdentity.Text = "";
            txtChargeType.Text = "";
            txtPrescAttr.Text = "";
            txtPrescDate.Text = "";
            txtUnitincontract.Text = "";
            txtPrescribed.Text = "";
            txtPrescSource.Text = "";
            txtOrderedby.Text = "";
            txtEnteredby.Text = "";
            txtRepetition.Text = "";
            txtCountRepetition.Text = "";
            txtDecoction.Text = "";
            txtBarcode.Text = "";
            gc2.DataSource = null;
        }
        /// <summary>
        /// 修改座位
        /// </summary>
        /// <returns></returns>
        private string PrescQueueSeatDict()
        {
            string prescdate = masterdr["PRESC_DATE"].ToString();
            object prescwinno = masterdr["presc_widno"];
            DataTable quedt = GetQueueSeatDictByPrescWidno(this.DeptCode, prescwinno);
            if (quedt.Rows.Count > 0)
            {
                return UpdateQueueSeatDict(this.DeptCode, prescwinno);
            }
            return "";
        }
        /// <summary>
        /// 初始化处方明细列名
        /// </summary>
        /// <returns></returns>
        private DataTable GetPrescDetailDataRow()
        {
            DataTable detaildt = new DataTable();
            detaildt.Columns.Add("PRESC_DATE");
            detaildt.Columns.Add("PRESC_NO");
            detaildt.Columns.Add("ITEM_NO");
            detaildt.Columns.Add("DRUG_CODE");
            detaildt.Columns.Add("DRUG_NAME");
            detaildt.Columns.Add("TRADE_NAME");
            detaildt.Columns.Add("DRUG_SPEC");
            detaildt.Columns.Add("UNITS");
            detaildt.Columns.Add("PACKAGE_SPEC");
            detaildt.Columns.Add("PACKAGE_UNITS");
            detaildt.Columns.Add("FIRM_ID");
            detaildt.Columns.Add("BATCH_NO");
            detaildt.Columns.Add("GEN_SERIAL");
            detaildt.Columns.Add("QUANTITY");
            detaildt.Columns.Add("INVENTORY");
            detaildt.Columns.Add("BATCH_CODE");
            detaildt.Columns.Add("EXPIRE_DATE");
            detaildt.Columns.Add("PURCHASE_PRICE");
            detaildt.Columns.Add("TRADE_PRICE");
            detaildt.Columns.Add("RETAIL_PRICE");
            detaildt.Columns.Add("SUPPLIER");
            detaildt.Columns.Add("COSTS");
            detaildt.Columns.Add("PAYMENTS");
            detaildt.Columns.Add("ROUND_AMT");
            detaildt.Columns.Add("ORDER_NO");
            detaildt.Columns.Add("ORDER_SUB_NO");
            detaildt.Columns.Add("ADMINISTRATION");
            detaildt.Columns.Add("DOSAGE_EACH");
            detaildt.Columns.Add("DOSAGE_UNITS");
            detaildt.Columns.Add("FREQUENCY");
            detaildt.Columns.Add("FREQ_DETAIL");
            detaildt.Columns.Add("HANDBACK_AMOUNT");
            detaildt.Columns.Add("INSUR_ADULT");
            detaildt.Columns.Add("GUID");
            //追溯码医保上传扩展
            //detaildt.Columns.Add("INSUR_SEQ");
            //detaildt.Columns.Add("OTC");
            //detaildt.Columns.Add("CZ");
            //detaildt.Columns.Add("PRODUCE_DATE");
            //detaildt.Columns.Add("DRUG_INDICATOR");
            return detaildt;
        }

        /// <summary>
        /// 叫号待摆药查询
        /// </summary>
        /// <returns></returns>
        /// 
        private int wf_disp_waitprepare()
        {
            int li_count;
            li_count = Comm.ClassPrescQueue.uf_client_get_count(this.DeptCode, is_seatcode, Comm.ClassPrescQueue.STATUS_WAIT_PREPARE);
            if (li_count > 0)
            {
                st_queue_wait = "待摆:" + li_count.ToString() + "人";
            }
            else
            {
                st_queue_wait = "待摆:" + "0" + "人";
            }
            Get_queue_wait(Comm.ClassPrescQueue.STATUS_WAIT_PREPARE);
            return 0;
        }
        private int Get_queue_wait(int ai_status)
        {
            string sql = "SELECT distinct PHARMACY.DRUG_PRESC_QUEUE.STORAGE,PHARMACY.DRUG_PRESC_QUEUE.PRESC_DATE,PHARMACY.DRUG_PRESC_QUEUE.PRESC_NO,";
            sql = sql + "PHARMACY.DRUG_PRESC_QUEUE.CONTENT,PHARMACY.DRUG_PRESC_QUEUE.STATUS,PHARMACY.DRUG_PRESC_QUEUE.NOTICEFLAG,";
            sql = sql + "PHARMACY.DRUG_PRESC_QUEUE.SEATCODE FROM PHARMACY.DRUG_PRESC_QUEUE ";
            sql = sql + " where storage='" + this.DeptCode + "' and seatcode='" + is_seatcode + "' and status=" + ai_status + " ";
            dt_queue_wait = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            return 0;
        }

        private int wf_disp_waitdispense()
        {

            st_queue_status = Comm.ClassPrescQueue.MSG_IDLE;
            messageinfo.Text = "   " + st_client_status + "|" + st_queue_status + "|" + st_queue_wait;
            return 0;
        }

        private int wf_get_client_status()
        {
            string ls_type = Comm.ClassPrescQueue.uf_get_client_status(is_seatcode);
            if (ls_type.Equals("1"))
            {
                st_client_status = "启用";
            }
            else
            {
                st_client_status = "停用";
            }

            return 0;
        }

        /// <summary>
        /// 界面初始化排队叫号数据查询
        /// </summary>
        /// <returns></returns>
        /// 
        private int init_queue_call()
        {
            Dictionary<string, string> idc = new Dictionary<string, string>();
            string ls_type_code, ls_win_date;
            DateTime ldt_sysdate;
            string sql = "select type_code ,win_date from DRUG_WINDOWS_NO_DICT where windows_no='" + is_windws_no + "' and drug_code='" + this.DeptCode + "'";
            DataTable dt = new ServerPublicClient().GetDataBySql(sql).Tables[0];
            if (dt.Rows.Count > 0)
            {
                ls_win_date = dt.Rows[0]["WIN_DATE"].ToString();
                ldt_sysdate = new ServerPublicClient().GetSysDate();
                if (DateTime.Parse(ls_win_date).Date == ldt_sysdate.Date)
                {
                    sql = "update drug_windows_no_dict set  num_no=0 , win_date=to_date('" + ldt_sysdate.ToString() + "','yyyy-mm-dd hh24:mi:ss') where windows_no='" + is_windws_no + "' and drug_code='" + this.DeptCode + "' ";
                    idc.Add(sql, "重置窗口号数据失败！");
                    string result = new NM_Service.NMService.PatientInDeptClient().SaveTable(idc);
                    if (!string.IsNullOrEmpty(result))
                    {
                        XtraMessageBox.Show(result, "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return -1;
                    }
                    else
                    {
                    }
                }

                ls_type_code = dt.Rows[0]["TYPE_CODE"].ToString();
                if (ls_type_code.Equals("0"))
                {
                    cb_start.Enabled = true;
                    cb_pause.Enabled = false;
                }
                else
                {
                    cb_start.Enabled = false;
                    cb_pause.Enabled = true;
                }

            }
            else
            {
                cb_start.Enabled = false;
            }

            if (!string.IsNullOrEmpty(is_seatcode))
            {

                wf_disp_waitprepare();
                wf_get_client_status();
                wf_disp_waitdispense();
            }

            return 0;
        }
        #endregion

        #region SQL

        /// <summary>
        /// 修改坐席字典
        /// </summary>
        /// <param name="storagecode"></param>
        /// <param name="prescwidno"></param>
        /// <returns></returns>
        private string UpdateQueueSeatDict(string storagecode, object prescwidno)
        {
            string sql = "update  queue_seat_dict set  curr_order_no = curr_order_no -1 where deptcode = '" + storagecode + "' and seatcode='" + prescwidno + "' ";
            return sql;
        }
        /// <summary>
        /// 获取坐席字典
        /// </summary>
        /// <param name="storagecode"></param>
        /// <param name="prescwidno"></param>
        /// <returns></returns>
        private DataTable GetQueueSeatDictByPrescWidno(string storagecode, object prescwidno)
        {
            string sql = "select nvl(curr_order_no,0) from  queue_seat_dict  where deptcode = :gs_storage_code and seatcode=:ls_presc_widno ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("gs_storage_code");
            para.Add("ls_presc_widno");
            para_val.Add(storagecode);
            para_val.Add(prescwidno);

            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 删除门诊待发药处方明细表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescDetailTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_detail_temp	WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "'";
            return sql;
        }
        /// <summary>
        /// 删除门诊待发药处方主表
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string DeleteDrugPrescMasterTemp(string prescdate, string prescno)
        {
            string sql = "DELETE drug_presc_master_temp	WHERE presc_date = to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss') AND presc_no = '" + prescno + "'";
            return sql;
        }
        /// <summary>
        /// 生成序号
        /// </summary>
        /// <returns></returns>
        private string GetGenSerialSeq()
        {
            //string sql = "select GEN_SERIAL_SEQ.nextval from dual";
            //DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            //if(dt.Rows.Count<1)
            //{
            //    return "";
            //}
            //else
            //{
            //    return dt.Rows[0][0] == DBNull.Value ? "" : dt.Rows[0][0].ToString();
            //}
            //改成从配置表取 by lions 2018-06-27
            #region
            string sysdate = PlatCommon.Common.PublicFunction.GetSysDate();
            string ls_gen_serail = "";
            if (!PlatCommon.Common.PublicFunction.GetSequeceFromAuto("药品出入库单号序列", PlatCommon.SysBase.SystemParm.HisUnitCode, ref ls_gen_serail) || string.IsNullOrEmpty(ls_gen_serail) || string.IsNullOrEmpty(sysdate))
            {
                return "";//获取序列失败
            }
            ls_gen_serail = DateTime.Parse(sysdate).ToString("yyMMdd") + ls_gen_serail;
            return ls_gen_serail;
            #endregion
        }
        /// <summary>
        ///修改药品库存 - 2025-01-17 修复：添加防止负库存的安全检查
        /// </summary>
        /// <returns></returns>
        private string UpdateDrugStock(decimal quantity, string storagecode, string drugcode, string drugspec,
            string packagespec, string firmid, string batchno, string batchcode, string retailprice)
        {
            // 2025-01-17 修复：确保扣减后库存不为负数
            // 原条件：QUANTITY > 0 只是查询条件，不能防止扣减后变负
            // 修复后：QUANTITY >= quantity 确保库存充足才能扣减
            string sql = "UPDATE DRUG_STOCK SET QUANTITY= QUANTITY - " + quantity + ",LAST_UPDATETIME=SYSDATE " +
                "WHERE STORAGE = '" + storagecode + "' AND QUANTITY >= " + quantity + " and drug_code = '" + drugcode + "' " +
                "and drug_spec = '" + drugspec + "' and package_spec = '" + packagespec + "' " +
                "and firm_id = '" + firmid + "'";// and BATCH_NO='" + batchno + "' and batch_code = '" + batchcode + "'";
            if (!string.IsNullOrEmpty(batchno))
            {
                sql += " and BATCH_NO='" + batchno + "'";
            }
            if (!string.IsNullOrEmpty(batchcode))
            {
                sql += " and BATCH_CODE='" + batchcode + "'";
            }
            if (!string.IsNullOrEmpty(retailprice))
            {
                sql += " and RETAIL_PRICE='" + retailprice + "'";
            }
            return sql;

        }

        private string UpdateDrug_presc_detail_temp(string storagecode, string drugcode, string drugspec,
            string packagespec, string firmid)
        {
            string sql = "";
            return sql;
        }
        /// <summary>
        /// 获取药品库存 AND S.SUPPLY_INDICATOR = 1
        /// </summary>
        /// <returns></returns>
        private void GetDrugStock(string storagecode, string drugcode, string drugspec, string packagespec, string firmid, string batchno, string batchcode, string itemprice, ref DataTable dtStock)
        //private DataTable GetDrugStock(string storagecode, string drugcode, string drugspec, string packagespec, string firmid,string batchno, string batchcode, string itemprice)
        {
            // 2025-01-03 修复：确保查询逻辑与门诊医生站GetDrugStockRetailPrice完全一致
            // 关键修改：
            // 1. 去掉QUANTITY > 0条件（与门诊医生站一致）
            // 2. 去掉零售价匹配条件（避免价格不一致导致查找失败）
            // 3. 使用ROWNUM = 1（与门诊医生站一致）
            // 4. 保持DRUG_DICT关联（为了获取DOSE_PER_UNIT等字段）
            string sql = "SELECT S.DRUG_CODE,S.DRUG_NAME,S.TRADE_NAME,D.DOSE_PER_UNIT,D.DOSE_UNITS,0.0000000000 orders_dosage_in_stock_units," +
                "S.DRUG_SPEC,S.UNITS,S.FIRM_ID,S.PACKAGE_SPEC,S.PACKAGE_UNITS,S.BATCH_NO,S.QUANTITY," +
                "S.BATCH_CODE,S.EXPIRE_DATE,S.PURCHASE_PRICE,S.DISCOUNT,S.TRADE_PRICE,S.RETAIL_PRICE," +
                "S.SUPPLIER,S.SUB_STORAGE,S.LOCATION_CODE,(select max(amount_per_package) " +
                "from CURRENT_DRUG_MD_PRICE_LIST temp where temp.drug_code = S.drug_code and temp.min_spec = S.drug_spec " +
                "and temp.drug_spec = S.package_spec and temp.firm_id = S.firm_id and temp.START_DATE <= sysdate " +
                "and (temp.STOP_DATE >= sysdate OR temp.STOP_DATE is null) and temp.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "') amount_per_package, " +
                "S.STORAGE,S.PACKAGE_1,S.PACKAGE_SPEC_1,S.PACKAGE_UNITS_1,S.PACKAGE_2,S.PACKAGE_SPEC_2," +
                "S.PACKAGE_UNITS_2,S.SUPPLY_INDICATOR,S.DOCUMENT_NO,S.PURCHASE_PRICE_LAST,S.FROZEN_FLAG,S.QUANTITY_PRE," +
                "S.LAST_UPDATETIME " +
                "FROM DRUG_STOCK S, DRUG_DICT D " +
                "WHERE S.DRUG_CODE = D.DRUG_CODE and D.DRUG_SPEC = S.DRUG_SPEC " +
                "and S.STORAGE = :as_storage_code " +
                "and S.HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' " +
                "and S.drug_code = :as_drug_code " +
                "and S.drug_spec = :as_drug_spec " +
                "and S.package_spec = :as_package_spec " +
                "and S.firm_id = :as_firm_id " +
                "and S.SUPPLY_INDICATOR = 1 " +
                // 2025-01-03 修复：保持QUANTITY > 0条件，确保库存管理的正确性
                // 处方发药时必须验证库存数量，避免负库存问题
                "AND S.QUANTITY > 0 ";
            if (!string.IsNullOrEmpty(batchno))
                sql += "and s.batch_no = :as_batch_no ";
            if (!string.IsNullOrEmpty(batchcode))
                sql += "and s.batch_code = :as_batch_code ";
            // 2025-07-20 修复：移除零售价严格匹配，避免价格微小差异导致查询失败
            // 实际业务中，同一药品不同批次的零售价可能存在微小差异
            // 应该以药品代码、规格、厂家为主要匹配条件
            // if (!string.IsNullOrEmpty(itemprice))
            //     sql += "and s.retail_price = :itemprice ";

            // 2025-07-20 关键修复：移除ROWNUM=1限制，允许返回所有符合条件的库存记录
            // 这是解决"只能看到部分库存"问题的核心修复
            // 原来的ROWNUM=1会导致只返回第一条记录，丢失其他批次的库存
            // sql += " AND ROWNUM = 1";

            // 2025-07-20 修复：恢复批次排序，优先使用快过期的批次
            sql += " order by S.EXPIRE_DATE asc, S.BATCH_NO asc";

            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_storage_code");
            para.Add("as_drug_code");
            para.Add("as_drug_spec");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            if (!string.IsNullOrEmpty(batchno))
                para.Add("as_batch_no");
            if (!string.IsNullOrEmpty(batchcode))
                para.Add("as_batch_code");
            // 2025-07-20 修复：移除零售价匹配参数，避免价格不一致导致查询失败
            // if (!string.IsNullOrEmpty(itemprice))
            //     para.Add("itemprice");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(packagespec);
            para_val.Add(firmid);
            if (!string.IsNullOrEmpty(batchno))
                para_val.Add(batchno);
            if (!string.IsNullOrEmpty(batchcode))
                para_val.Add(batchcode);
            // 2025-07-20 修复：移除零售价匹配参数值，避免价格不一致导致查询失败
            // if (!string.IsNullOrEmpty(itemprice))
            //     para_val.Add(itemprice);
            //DataSet ds = new DataSet();
            //ds = spc.GetDataTable_Para(sql, para, para_val);
            //return ds.Tables[0];

            //231020
            if (dtStock == null)
            {
                dtStock = spc.GetDataTable_Para(sql, para, para_val).Tables[0];
                dtStock.AcceptChanges();
            }
            else
            {
                dtStock.Load(new DataTableReader(spc.GetDataTable_Para(sql, para, para_val).Tables[0]));
            }
        }

        /// <summary>
        /// 获取与门诊医生站完全一致的药品零售价
        /// 2025-01-03 新增：确保处方发药系统与门诊医生站使用相同的价格选择逻辑
        /// </summary>
        /// <param name="drugCode">药品代码</param>
        /// <param name="packageSpec">包装规格</param>
        /// <param name="firmId">厂家ID</param>
        /// <param name="storage">库房代码</param>
        /// <returns>零售价</returns>
        private decimal GetDrugStockRetailPriceConsistent(string drugCode, string packageSpec, string firmId, string storage)
        {
            try
            {
                // 使用与门诊医生站GetDrugStockRetailPrice完全相同的SQL
                string sql = @"SELECT NVL(RETAIL_PRICE, 0) AS RETAIL_PRICE
                              FROM DRUG_STOCK
                              WHERE STORAGE = :STORAGE
                                AND DRUG_CODE = :DRUG_CODE
                                AND PACKAGE_SPEC = :PACKAGE_SPEC
                                AND FIRM_ID = :FIRM_ID
                                AND HIS_UNIT_CODE = :HIS_UNIT_CODE
                                AND SUPPLY_INDICATOR = 1
                                AND ROWNUM = 1";

                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                List<string> para = new List<string>();
                ArrayList para_val = new ArrayList();
                para.Add("STORAGE");
                para.Add("DRUG_CODE");
                para.Add("PACKAGE_SPEC");
                para.Add("FIRM_ID");
                para.Add("HIS_UNIT_CODE");
                para_val.Add(storage);
                para_val.Add(drugCode);
                para_val.Add(packageSpec);
                para_val.Add(firmId);
                para_val.Add(SystemParm.HisUnitCode);

                DataSet ds = spc.GetDataTable_Para(sql, para, para_val);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    decimal price = 0;
                    if (decimal.TryParse(ds.Tables[0].Rows[0]["RETAIL_PRICE"].ToString(), out price))
                        return price;
                }
                return 0;
            }
            catch (Exception ex)
            {
                Utility.LogFile.WriteLogAuto($"获取一致性零售价失败: 药品代码={drugCode}, 规格={packageSpec}, 厂家={firmId}, 库房={storage}, 错误={ex.Message}", "处方发药");
                return 0;
            }
        }

        /// <summary>
        /// 添加处方明细表
        /// </summary>
        /// <param name="ddr"></param>
        /// <returns></returns>
        private string InsertDrugPrescDetail(DataRow ddr)
        {
            string sql = "Insert into DRUG_PRESC_DETAIL(  PRESC_DATE,PRESC_NO,ITEM_NO,DRUG_CODE,DRUG_NAME,TRADE_NAME," +
                "DRUG_SPEC,UNITS,PACKAGE_SPEC,PACKAGE_UNITS,FIRM_ID,BATCH_NO," +
                "GEN_SERIAL,QUANTITY,INVENTORY,BATCH_CODE,EXPIRE_DATE," +
                "PURCHASE_PRICE,TRADE_PRICE,RETAIL_PRICE,SUPPLIER,COSTS,PAYMENTS," +
                "ROUND_AMT,ORDER_NO,ORDER_SUB_NO,ADMINISTRATION,DOSAGE_EACH,DOSAGE_UNITS," +
                "FREQUENCY,FREQ_DETAIL,HANDBACK_AMOUNT,INSUR_ADULT ,GUID,HIS_UNIT_CODE ) " +
                "values (to_date('" + ddr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'" + ddr["PRESC_NO"] + "','" + ddr["ITEM_NO"] + "','" + ddr["DRUG_CODE"] + "','" + ddr["DRUG_NAME"] + "','" + ddr["TRADE_NAME"] + "','"
                + ddr["DRUG_SPEC"] + "','" + ddr["UNITS"] + "','" + ddr["PACKAGE_SPEC"] + "','" + ddr["PACKAGE_UNITS"] + "','" + ddr["FIRM_ID"] + "','" + ddr["BATCH_NO"] + "','"
                + ddr["GEN_SERIAL"] + "','" + ddr["QUANTITY"] + "','" + ddr["INVENTORY"] + "','" + ddr["BATCH_CODE"] + "',to_date('" + ddr["EXPIRE_DATE"] + "','yyyy-MM-dd'),'"
                + ddr["PURCHASE_PRICE"] + "','" + ddr["TRADE_PRICE"] + "','" + ddr["RETAIL_PRICE"] + "','" + ddr["SUPPLIER"] + "','" + ddr["COSTS"] + "','" + ddr["PAYMENTS"] + "','"
                + ddr["ROUND_AMT"] + "','" + ddr["ORDER_NO"] + "','" + ddr["ORDER_SUB_NO"] + "','" + ddr["ADMINISTRATION"] + "','" + ddr["DOSAGE_EACH"] + "','" + ddr["DOSAGE_UNITS"] + "','"
                + ddr["FREQUENCY"] + "','" + ddr["FREQ_DETAIL"] + "','" + ddr["HANDBACK_AMOUNT"] + "','" + ddr["INSUR_ADULT"] + "','" + ddr["GUID"] + "','" + SystemParm.HisUnitCode + "')";
            return sql;
        }
        /// <summary>
        /// 添加处方主表
        /// </summary>
        /// <param name="mdr"></param>
        /// <returns></returns>
        private string InsertDrugPrescMaster(DataRow mdr)
        {
            string sql = "INSERT INTO DRUG_PRESC_MASTER(PRESC_DATE,PRESC_NO,DISPENSARY,PATIENT_ID,VISIT_ID,";
            sql += "CLINIC_NO,NAME,NAME_PHONETIC,AGE,IDENTITY,CHARGE_TYPE,UNIT_IN_CONTRACT,DIAG_DESC,";
            sql += "PRESC_TYPE,PRESC_ATTR,PRESC_SOURCE,DISCHARGE_TAKING_INDICATOR,DECOCTION,REPETITION,";
            sql += "COUNT_PER_REPETITION,ORDERED_BY,DOCTOR_USER,PRESCRIBED_USERCODE,PRESCRIBED_BY,";
            sql += "ENTERED_USERCODE,ENTERED_BY,ENTERED_DATETIME,VERIFY_USERCODE,VERIFY_BY,VERIFIED_DATETIME,";
            sql += "RCPT_NO,COSTS,PAYMENTS,ROUND_AMT,DISPENSING_USERCODE,DISPENSING_PROVIDER,DISPENSING_DATETIME,";
            sql += "FLAG,ORIGINAL_PRESC_DATE,ORIGINAL_PRESC_NO,DRUG_WIN_NO,";
            sql += "PRESC_WIDNO,QUEUE_ID,DISP_USER,DISP_USER_NAME,DISP_DATE_TIME,CRITICAL,MEMO,sex,HIS_UNIT_CODE) ";
            sql += "VALUES (to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss'),'";
            sql += mdr["PRESC_NO"] + "','" + mdr["DISPENSARY"] + "','" + mdr["PATIENT_ID"] + "' ,'" + "0";
            sql += "' ,'" + mdr["CLINIC_NO"] + "' ,'" + mdr["NAME"] + "' ,'" + mdr["NAME_PHONETIC"] + "' ,'";
            sql += mdr["AGE"] + "' ,'" + mdr["IDENTITY"] + "' ,'" + mdr["CHARGE_TYPE"] + "' ,'";
            sql += mdr["UNIT_IN_CONTRACT"] + "' ,'" + mdr["DIAG_DESC"] + "' ,'" + mdr["PRESC_TYPE"] + "' ,'";
            sql += mdr["PRESC_ATTR"] + "' ,'" + mdr["PRESC_SOURCE"] + "' ,'" + mdr["DISCG_TAKING_INDICATOR"];
            sql += "' ,'" + mdr["DECOCTION"] + "' ,'" + mdr["REPETITION"] + "' ,'" + mdr["COUNT_PER_REPETITION"];
            sql += "' ,'" + mdr["ORDERED_BY"] + "' ,'" + mdr["DOCTOR_USER"] + "' ,'" + mdr["PRESCRIBED_USERCODE"];
            sql += "' ,'" + mdr["PRESCRIBED_BY"] + "' ,'" + mdr["ENTERED_USERCODE"] + "' ,'" + mdr["ENTERED_BY"];
            sql += "' ,to_date('" + mdr["ENTERED_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss') ,'";
            sql += mdr["VERIFY_USERCODE"] + "' ,'" + mdr["VERIFY_BY"] + "' ,to_date('" + mdr["VERIFIED_DATETIME"].ToString() + "','yyyy-MM-dd HH24:mi:ss')";
            sql += "  ,'" + mdr["RCPT_NO"] + "' ,'" + mdr["COSTS"] + "' ,'" + mdr["PAYMENTS"] + "' ,'";
            sql += mdr["ROUND_AMT"] + "' ,'" + mdr["DISPENSING_USERCODE"] + "' ,'" + mdr["DISPENSING_PROVIDER"];
            sql += "' ,to_date('" + mdr["DISPENSING_DATETIME"] + "','yyyy-MM-dd HH24:mi:ss') ,'1' ,to_date('" + mdr["PRESC_DATE"] + "','yyyy-MM-dd HH24:mi:ss') ,'";
            sql += mdr["PRESC_NO"] + "'";
            sql += ",'" + mdr["DRUG_WIN_NO"] + "' ,'" + mdr["PRESC_WIDNO"] + "' ,'";
            sql += mdr["QUEUE_ID"] + "' ,'" + mdr["DISP_USER"] + "' ,'" + mdr["DISP_USER_NAME"] + "' ,to_date('";
            sql += mdr["DISP_DATE_TIME"] + "','yyyy-MM-dd HH24:mi:ss'),'" + mdr["CRITICAL"] + "' ,'" + mdr["MEMO"] + "','" + mdr["sex"] + "','" + SystemParm.HisUnitCode + "' )";
            return sql;
        }
        /// <summary>
        /// 获取药品库存
        /// </summary>
        /// <param name="storagecode"></param>
        /// <param name="drugcode"></param>
        /// <param name="firmid"></param>
        /// <param name="packagespec"></param>
        /// <returns></returns>
        private DataTable GetDrugStockByDrugCode(string storagecode, string drugcode, string firmid, string packagespec)
        {
            string sql = "select sum(drug_stock.quantity) FROM drug_stock where  HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "'and drug_stock.storage= :gs_storage_code and drug_stock.drug_code= :ls_drug_code and drug_stock.firm_id = :ls_firm_id  and drug_stock.PACKAGE_SPEC  = :ls_package_spec";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("gs_storage_code");
            para.Add("ls_drug_code");
            para.Add("ls_firm_id");
            para.Add("ls_package_spec");
            para_val.Add(storagecode);
            para_val.Add(drugcode);
            para_val.Add(firmid);
            para_val.Add(packagespec);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 获取药品价格 - 2025-01-11 统一从DRUG_STOCK表获取，按最新入库、有效期、库存量、批次号排序
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="drugspec"></param>
        /// <param name="firmid"></param>
        /// <returns></returns>
        private DataTable GetDrugPriceDrugCode(string drugcode, string drugspec, string firmid)
        {
            string sql = @"SELECT TRADE_PRICE, RETAIL_PRICE FROM (
                          SELECT TRADE_PRICE, RETAIL_PRICE
                          FROM DRUG_STOCK
                          WHERE DRUG_CODE = :as_DRUG_CODE
                          AND PACKAGE_SPEC = :as_package_spec
                          AND FIRM_ID = :as_firm_id
                          AND HIS_UNIT_CODE = '" + SystemParm.HisUnitCode + @"'
                          AND SUPPLY_INDICATOR = 1
                          AND QUANTITY > 0
                          ORDER BY DOCUMENT_NO DESC, EXPIRE_DATE ASC, QUANTITY ASC, BATCH_NO ASC
                          ) WHERE ROWNUM = 1";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("as_DRUG_CODE");
            para.Add("as_package_spec");
            para.Add("as_firm_id");
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(firmid);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 修改发药状态
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private string UpdateDrugPrescQueue(string prescdate, string prescno)
        {

            string sql = "update drug_presc_queue  set   status= 4  where presc_date=to_date('" + prescdate + "','yyyy-MM-dd HH24:mi:ss')  and presc_no ='" + prescno + "'";

            return sql;
        }
        /// <summary>
        /// 获取药品用量信息
        /// </summary>
        /// <param name="drugcode"></param>
        /// <param name="drugspec"></param>
        /// <param name="administration"></param>
        /// <returns></returns>
        private DataTable GetDrugRationalDosage(string drugcode, string drugspec, string administration)
        {
            string sql = "select MAX_PRESC_DOSAGE,MAX_OUTP_ABIDANCE  from DRUG_RATIONAL_DOSAGE where drug_code=:ls_drug_code and drug_spec = :ls_drug_spec and ADMINISTRATION = :ls_adminitrator";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_drug_code");
            para.Add("ls_drug_spec");
            para.Add("ls_adminitrator");
            para_val.Add(drugcode);
            para_val.Add(drugspec);
            para_val.Add(administration);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 返回医嘱执行频率字典
        /// </summary>
        /// <param name="frequency"></param>
        /// <returns></returns>
        private DataTable GetPerformFreqDict(string frequency)
        {
            string sql = "select FREQ_COUNTER,FREQ_INTERVAL,FREQ_INTERVAL_UNITS,FREQ_DESC from PERFORM_FREQ_DICT 	 where FREQ_DESC = :ls_FREQUENCY ";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("ls_FREQUENCY");
            para_val.Add(frequency);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 根据处方号和时间获取处方明细信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetPrescDetailTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = @"
                SELECT distinct D.PRESC_DATE,
                       D.PRESC_NO,
                       D.ITEM_NO,
                       D.DRUG_CODE,
                       D.DRUG_NAME,
                       D.TRADE_NAME,
                       D.DRUG_SPEC,
                       D.UNITS,
                       D.PACKAGE_SPEC,
                       D.PACKAGE_UNITS,
                       D.FIRM_ID,
                       D.BATCH_NO,
                       D.BATCH_CODE,
                       D.GUID,
                       D.EXPIRE_DATE,
                       D.PURCHASE_PRICE,
                       D.TRADE_PRICE,
                       D.RETAIL_PRICE,
                       D.SUPPLIER,
                       D.QUANTITY,
                       D.COSTS,
                       D.PAYMENTS,
                       D.ROUND_AMT,
                       D.ORDER_NO,
                       D.ORDER_SUB_NO,
                       D.ADMINISTRATION,
                       D.DOSAGE_EACH,
                       D.DOSAGE_UNITS,
                       D.FREQUENCY,
                       D.FREQ_DETAIL,
                       '' describe,
                       E.DIAG_DESC,
                       (select  t.money_type from outp_payments_money t where t.rcpt_no = e.rcpt_no and rownum = 1) money_type,
                   (select tt.insur_code from insurance.tj_vs_price tt 
where d.drug_code =tt.item_code 
and tt.item_spec =d.package_spec||d.firm_id   
and  d.package_units=tt.units and  rownum =1) insur_code,
(select tt.insur_name from insurance.tj_vs_price tt 
where d.drug_code =tt.item_code 
and  tt.item_spec =d.package_spec||d.firm_id   
and d.package_units=tt.units and  rownum =1) insur_name,
D.INSUR_ADULT ,
(SELECT INSUR_SEQ FROM INSURANCE.TJ_VS_PRICE T WHERE ITEM_SEQ = DRUG_CODE AND DRUG_SPEC = PACKAGE_SPEC AND ROWNUM=1) INSUR_SEQ,
(SELECT OTC FROM drug_dict dd WHERE dd.drug_code = d.drug_code AND dd.drug_spec = PACKAGE_SPEC and rownum=1) OTC,
  (select listagg(DRUG_TRACE_CODE,'|') within group(order by DRUG_TRACE_CODE)as TRACE_CODES 
    from DRUG_PRESC_TRACK where DRUG_CODE=d.DRUG_CODE and DRUG_SPEC=d.PACKAGE_SPEC and FIRM_ID=d.FIRM_ID and UNITS=d.PACKAGE_UNITS  and TRACK_CODE=to_char(D.PRESC_DATE,'yyyy-MM-dd hh24:mi:ss')||'_'||D.PRESC_NO and TRACK_TYPE='门诊发药') CZ,
 (SELECT PRODUCE_DATE FROM DRUG_IMPORT_DETAIL I 
                  WHERE I.DRUG_CODE=D.DRUG_CODE 
                  AND I.DRUG_SPEC=D.DRUG_SPEC 
                  AND I.UNITS=D.UNITS 
                  AND I.PACKAGE_SPEC=D.PACKAGE_SPEC AND I.PACKAGE_UNITS=D.PACKAGE_UNITS AND ROWNUM=1 )PRODUCE_DATE,                 
(select DRUG_INDICATOR from drug_dict where drug_code=d.drug_code and rownum=1)DRUG_INDICATOR
                  FROM DRUG_PRESC_DETAIL_TEMP D,DRUG_PRESC_MASTER_TEMP E,OUTP_PAYMENTS_MONEY T
                 WHERE D.PRESC_DATE=E.Presc_Date 
                   AND D.PRESC_NO =E.PRESC_NO 
                   AND T.RCPT_NO = E.RCPT_NO 
                   AND D.PRESC_DATE = :adt_presc_date
                   AND D.PRESC_NO = :al_presc_no ";
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_presc_date");
            para.Add("al_presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }

        /// <summary>
        /// 根据处方号和时间获取处方主信息
        /// </summary>
        /// <param name="prescdate"></param>
        /// <param name="prescno"></param>
        /// <returns></returns>
        private DataTable GetPrescMasterTempByDateAndNo(DateTime prescdate, string prescno)
        {
            string sql = @"
                SELECT D.PRESC_DATE,
                       D.PRESC_NO,
                       D.DISPENSARY,
                       D.PATIENT_ID,
                       D.VISIT_ID,
                       D.CLINIC_NO,
                       D.NAME,
                       D.NAME_PHONETIC,
                       D.IDENTITY,
                       D.CHARGE_TYPE,
                       D.UNIT_IN_CONTRACT,
                       D.DIAG_DESC,
                       D.PRESC_TYPE,
                       D.PRESC_ATTR,
                       D.PRESC_SOURCE,
                       D.DISCG_TAKING_INDICATOR,
                       D.DECOCTION AS DECOCTION,
                       D.COUNT_PER_REPETITION AS COUNT_PER_REPETITION,
                       D.REPETITION,
                       D.ORDERED_BY,
                       D.DOCTOR_USER,
                       D.PRESCRIBED_USERCODE,
                       D.PRESCRIBED_BY,
                       D.ENTERED_USERCODE,
                       D.ENTERED_BY,
                       D.ENTERED_DATETIME,
                       D.VERIFY_USERCODE,
                       D.VERIFY_BY,
                       D.VERIFIED_DATETIME,
                       D.RCPT_NO,
                       D.COSTS,
                       D.PAYMENTS,
                       D.ROUND_AMT,
                       D.CHARGE_INDICATOR,
                       D.DISPENSING_USERCODE,
                       D.DISPENSING_PROVIDER,
                       D.DISPENSING_DATETIME,
                       D.STATUS,
                       D.DRUG_WIN_NO,
                       D.PRESC_WIDNO,
                       D.QUEUE_ID,
                       D.PRINT_DATE_TIME,
                       D.DISP_USER,
                       D.DISP_USER_NAME,
                       D.DISP_DATE_TIME,
                       D.CRITICAL,
                       D.MEMO,
                       D.REFUNDED_PRESC_DATE,
                       D.REFUNDED_PRESC_NO,
                       E.DEPT_NAME,
                       M.SEX,
                       M.DATE_OF_BIRTH,
                       FLOOR(MONTHS_BETWEEN(SYSDATE, M.DATE_OF_BIRTH) / 12) AGE,
                       M.DATE_OF_BIRTH,
(select WORK_ID from staff_dict t where d.PRESCRIBED_USERCODE=t.EMP_NO and rownum=1 )WORK_ID,
                       ENTERED_BY as ENTERED_NAME
                  FROM DRUG_PRESC_MASTER_TEMP D
                  LEFT JOIN COMM.DEPT_DICT E
                    ON D.ORDERED_BY = E.DEPT_CODE
                  LEFT JOIN PAT_MASTER_INDEX M
                    ON D.PATIENT_ID = M.PATIENT_ID
                 WHERE D.PRESC_DATE = :adt_presc_date
                   and D.PRESC_NO = :al_presc_no ";
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_presc_date");
            para.Add("al_presc_no");
            para_val.Add(prescdate);
            para_val.Add(prescno);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];

            //string sql = @"
            //    SELECT D.PRESC_DATE,
            //           D.PRESC_NO,
            //           D.DISPENSARY,
            //           D.PATIENT_ID,
            //           D.VISIT_ID,
            //           D.CLINIC_NO,
            //           D.NAME,
            //           D.NAME_PHONETIC,
            //           D.IDENTITY,
            //           D.CHARGE_TYPE,
            //           D.UNIT_IN_CONTRACT,
            //           D.DIAG_DESC,
            //           D.PRESC_TYPE,
            //           D.PRESC_ATTR,
            //           D.PRESC_SOURCE,
            //           D.DISCG_TAKING_INDICATOR,
            //           NVL(D.DECOCTION,
            //               (SELECT OUTP_ORDERS.DECOCTION
            //                  FROM OUTP_ORDERS
            //                 WHERE OUTP_ORDERS.CLINIC_NO = D.CLINIC_NO
            //                   AND OUTP_ORDERS.APPOINT_NO = D.PRESC_NO
            //                   AND ROWNUM = 1)) DECOCTION,
            //           NVL(D.COUNT_PER_REPETITION,
            //               (SELECT OUTP_ORDERS.COUNT_PER_REPETITION
            //                  FROM OUTP_ORDERS
            //                 WHERE OUTP_ORDERS.CLINIC_NO = D.CLINIC_NO
            //                   AND OUTP_ORDERS.APPOINT_NO = D.PRESC_NO
            //                   AND ROWNUM = 1)) COUNT_PER_REPETITION,
            //           D.REPETITION,
            //           D.ORDERED_BY,
            //           D.DOCTOR_USER,
            //           D.PRESCRIBED_USERCODE,
            //           D.PRESCRIBED_BY,
            //           D.ENTERED_USERCODE,
            //           D.ENTERED_BY,
            //           D.ENTERED_DATETIME,
            //           D.VERIFY_USERCODE,
            //           D.VERIFY_BY,
            //           D.VERIFIED_DATETIME,
            //           D.RCPT_NO,
            //           D.COSTS,
            //           D.PAYMENTS,
            //           D.ROUND_AMT,
            //           D.CHARGE_INDICATOR,
            //           D.DISPENSING_USERCODE,
            //           D.DISPENSING_PROVIDER,
            //           D.DISPENSING_DATETIME,
            //           D.STATUS,
            //           D.DRUG_WIN_NO,
            //           D.PRESC_WIDNO,
            //           D.QUEUE_ID,
            //           D.PRINT_DATE_TIME,
            //           D.DISP_USER,
            //           D.DISP_USER_NAME,
            //           D.DISP_DATE_TIME,
            //           D.CRITICAL,
            //           D.MEMO,
            //           D.REFUNDED_PRESC_DATE,
            //           D.REFUNDED_PRESC_NO,
            //           E.DEPT_NAME,
            //           M.SEX,
            //           FLOOR(MONTHS_BETWEEN(SYSDATE, M.DATE_OF_BIRTH) / 12) AGE,
            //           M.DATE_OF_BIRTH,
            //           ENTERED_BY as ENTERED_NAME
            //      FROM DRUG_PRESC_MASTER_TEMP D
            //      LEFT JOIN COMM.DEPT_DICT E
            //        ON D.ORDERED_BY = E.DEPT_CODE
            //      LEFT JOIN PAT_MASTER_INDEX M
            //        ON D.PATIENT_ID = M.PATIENT_ID
            //     WHERE D.PRESC_DATE = :adt_presc_date
            //       and D.PRESC_NO = :al_presc_no ";
            //NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            //List<string> para = new List<string>();
            //ArrayList para_val = new ArrayList();
            //para.Add("adt_presc_date");
            //para.Add("al_presc_no");
            //para_val.Add(prescdate);
            //para_val.Add(prescno);
            //DataSet ds = new DataSet();
            //ds = spc.GetDataTable_Para(sql, para, para_val);
            //return ds.Tables[0];
        }

        /// <summary>
        /// 返回系统时间
        /// </summary>
        /// <returns></returns>
        private DateTime GetSysTime()
        {
            string sql = "select sysdate  from dual";
            DataTable dt = new ServerPublicClient().GetList(sql).Tables[0];
            return Convert.ToDateTime(dt.Rows[0][0]);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <param name="chargearray"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterTempByChargeFlag1Verified1(DateTime begintime, DateTime endtime, string storagecode, string chargearray)
        {
            string sql = "SELECT D.PRESC_NO,D.NAME,D.NAME_PHONETIC,D.PRESC_DATE,D.PAYMENTS,D.PATIENT_ID,D.PRESC_ATTR,";
            sql += "D.RCPT_NO,D.CLINIC_NO,D.VISIT_ID,E.DEPT_NAME FROM DRUG_PRESC_MASTER_TEMP D,DEPT_DICT E ";
            sql += " WHERE D.ORDERED_BY = E.DEPT_CODE and D.PRESC_DATE >= :adt_begin and D.PRESC_DATE <= :adt_end and ";
            sql += " D.DISPENSARY = :as_storage_code and D.CHARGE_TYPE in (:as_charge_array) and ";
            sql += " D.STATUS = 1 and (D.presc_source = 0 OR D.presc_source is null) and ";
            sql += " (D.REFUNDED_PRESC_DATE is null) and (D.REFUNDED_PRESC_NO = 0 OR D.REFUNDED_PRESC_NO is null)";
            //sql += " and D.DISPENSARY = '"+PlatCommon.System.PresdispParm.DeptCode+"'";
            if (rg_windws_check.EditValue.Equals("1")) //本窗口数据
            {
                sql = sql + " and  windows_no ='" + is_windws_no + "'";
            }
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            sql = sql + " order by D.PRESC_DATE desc";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_begin");
            para.Add("adt_end");
            para.Add("as_storage_code");
            para.Add("as_charge_array");
            para_val.Add(begintime);
            para_val.Add(endtime);
            para_val.Add(storagecode);
            para_val.Add(chargearray);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <param name="chargearray"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterTempByChargeFlag1Verified0(DateTime begintime, DateTime endtime, string storagecode, string chargearray)
        {
            string sql = "SELECT D.PRESC_NO,D.NAME,D.NAME_PHONETIC,D.PRESC_DATE,D.PAYMENTS,D.PATIENT_ID,D.PRESC_ATTR,";
            sql += "D.RCPT_NO,D.CLINIC_NO,D.VISIT_ID,E.DEPT_NAME FROM DRUG_PRESC_MASTER_TEMP D,DEPT_DICT E WHERE ";
            sql += "D.ORDERED_BY = E.DEPT_CODE and D.PRESC_DATE >= :adt_begin and D.PRESC_DATE <= :adt_end and D.DISPENSARY = :as_storage_code ";
            sql += " and D.CHARGE_TYPE in (:as_charge_array) and (D.presc_source = 0 OR D.presc_source is null) ";
            sql += " and (D.REFUNDED_PRESC_DATE is null) and (D.REFUNDED_PRESC_NO = 0 OR D.REFUNDED_PRESC_NO is null) ";
            //sql += " and D.DISPENSARY = '" + PlatCommon.System.SystemParm.Deptcode + "'"; 
            if (rg_windws_check.EditValue.Equals("1")) //本窗口数据
            {
                sql = sql + " and  windows_no ='" + is_windws_no + "'";
            }
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            sql = sql + " order by D.PRESC_DATE desc";

            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_begin");
            para.Add("adt_end");
            para.Add("as_storage_code");
            para.Add("as_charge_array");
            para_val.Add(begintime);
            para_val.Add(endtime);
            para_val.Add(storagecode);
            para_val.Add(chargearray);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <param name="chargearray"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterTempByChargeFlag0Verified1(DateTime begintime, DateTime endtime, string storagecode)
        {
            string sql = "SELECT D.PRESC_NO,D.NAME,D.NAME_PHONETIC,D.PRESC_DATE,D.PAYMENTS,D.PATIENT_ID,D.PRESC_ATTR,";
            sql += "D.RCPT_NO,D.CLINIC_NO,D.VISIT_ID,E.DEPT_NAME FROM DRUG_PRESC_MASTER_TEMP D,DEPT_DICT E WHERE ";
            sql += "D.ORDERED_BY = E.DEPT_CODE and D.PRESC_DATE >= :adt_begin and D.PRESC_DATE <= :adt_end and D.DISPENSARY = :as_storage_code ";
            sql += " and D.STATUS = 1 and (D.presc_source = 0 OR D.presc_source is null) and ";
            sql += " (D.REFUNDED_PRESC_DATE is null) and (D.REFUNDED_PRESC_NO = 0 OR D.REFUNDED_PRESC_NO is null)  ";
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            //sql += " and D.DISPENSARY = '" + PlatCommon.System.SystemParm.Deptcode + "'";
            if (rg_windws_check.EditValue.Equals("1")) //本窗口数据
            {
                sql = sql + " and  windows_no ='" + is_windws_no + "'";
            }
            sql = sql + " order by D.PRESC_DATE desc";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_begin");
            para.Add("adt_end");
            para.Add("as_storage_code");
            para_val.Add(begintime);
            para_val.Add(endtime);
            para_val.Add(storagecode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <param name="chargearray"></param>
        /// <returns></returns>
        private DataTable GetDrugPrescMasterTempByChargeFlag0Verified0(DateTime begintime, DateTime endtime, string storagecode)
        {
            string sql = "SELECT D.PRESC_NO,D.NAME,D.NAME_PHONETIC,D.PRESC_DATE,D.PAYMENTS,D.PATIENT_ID,D.RCPT_NO,D.PRESC_ATTR,";
            sql += "D.PRESC_WIDNO,D.CLINIC_NO,D.VISIT_ID,E.DEPT_NAME FROM DRUG_PRESC_MASTER_TEMP D,DEPT_DICT E ";
            sql += " WHERE D.ORDERED_BY = E.DEPT_CODE and D.PRESC_DATE >= :adt_begin and D.PRESC_DATE <= :adt_end and D.DISPENSARY = :as_storage_code and ";
            sql += " (D.presc_source = 0 OR D.presc_source is null) and (D.REFUNDED_PRESC_DATE is null) ";
            sql += " and (D.REFUNDED_PRESC_NO = 0 OR D.REFUNDED_PRESC_NO is null) ";
            sql += "  and D.His_Unit_Code='" + SystemParm.HisUnitCode + "'";
            //sql += " and D.DISPENSARY = '" + PlatCommon.System.SystemParm.Deptcode + "'";
            if (rg_windws_check.EditValue.Equals("1")) //本窗口数据
            {
                sql = sql + " and   windows_no ='" + is_windws_no + "'";
            }
            sql = sql + " order by D.PRESC_DATE desc";
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            List<string> para = new List<string>();
            ArrayList para_val = new ArrayList();
            para.Add("adt_begin");
            para.Add("adt_end");
            para.Add("as_storage_code");
            para_val.Add(begintime);
            para_val.Add(endtime);
            para_val.Add(storagecode);
            DataSet ds = new DataSet();
            ds = spc.GetDataTable_Para(sql, para, para_val);
            return ds.Tables[0];
        }
        /// <summary>
        /// 删除待发药队列
        /// </summary>
        /// <param name="begintime"></param>
        /// <param name="endtime"></param>
        /// <param name="storagecode"></param>
        /// <param name="chargearray"></param>
        /// <returns></returns>
        private string DeletePrescQueue(string presc_date, string presc_no)
        {
            string sql = "delete drug_presc_queue where presc_date=to_date('" + presc_date + "','yyyy-mm-dd hh24:mi:ss') and presc_no=" + presc_no + "";

            return sql;
        }
        #endregion

        private void gv2_RowCellClick(object sender, DevExpress.XtraGrid.Views.Grid.RowCellClickEventArgs e)
        {

        }
        [DllImport("User32.dll")]
        private static extern bool SetCursorPos(int x, int y);
        private void txtBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (gv2.RowCount < 1) return;
                string ls_barcode_no = txtBarcode.EditValue.ToString();
                string sql;
                NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
                if (!string.IsNullOrEmpty(ls_barcode_no))
                {
                    sql = "select count(*)  from CURRENT_DRUG_MD_PRICE_LIST t where (t.barcode_no ='" + ls_barcode_no + "' or substr(t.supervision_code, 1, 7) = substr('" + ls_barcode_no + "', 1, 7)) and	t.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and t.START_DATE <= SYSDATE and(t.STOP_DATE IS NULL OR(t.STOP_DATE IS NOT NULL AND t.STOP_DATE > SYSDATE))";
                    int ll_count = int.Parse(spc.GetSingleValue(sql));//电子注册码取前7位
                    if (ll_count > 1)
                    {
                        XtraMessageBox.Show("此条码找到多个药!", "提示", MessageBoxButtons.OK);
                        return;
                    }
                    if (ll_count > 0)//有此药品
                    {
                        sql = "select DRUG_CODE,MIN_SPEC,DRUG_SPEC,FIRM_ID  from CURRENT_DRUG_MD_PRICE_LIST t ";
                        sql += "where(t.barcode_no ='" + ls_barcode_no + "' or substr(t.supervision_code, 1, 7) = substr('" + ls_barcode_no + "', 1, 7)) and	t.his_unit_code='" + PlatCommon.SysBase.SystemParm.HisUnitCode + "' and t.START_DATE <= SYSDATE ";
                        sql += "and(t.STOP_DATE IS NULL OR(t.STOP_DATE IS NOT NULL AND t.STOP_DATE > SYSDATE)) and rownum = 1 ";
                        DataTable dt = spc.GetDataBySql(sql).Tables[0];
                        if (dt.Rows.Count < 1)
                        {
                            XtraMessageBox.Show("未找到此药品信息!", "提示", MessageBoxButtons.OK);
                            txtBarcode.EditValue = "";
                            //txtBarcode.Focus();
                            return;
                        }
                        string ls_drug_code = dt.Rows[0]["DRUG_CODE"].ToString();
                        string ls_drug_spec = dt.Rows[0]["MIN_SPEC"].ToString();
                        string ls_package_spec = dt.Rows[0]["DRUG_SPEC"].ToString();
                        string ls_firm_id = dt.Rows[0]["FIRM_ID"].ToString();
                        string ls_find = " drug_code='" + ls_drug_code + "' and drug_spec='" + ls_drug_spec + "' and package_spec='" + ls_package_spec + "' and firm_id='" + ls_firm_id + "'";
                        DataTable dt_gv2 = gc2.DataSource as DataTable;
                        DataRow[] drs = dt_gv2.Select(ls_find);
                        if (drs.Length <= 0)
                        {
                            XtraMessageBox.Show("条码号无法吻合，请确定药品!", "提示", MessageBoxButtons.OK);
                            txtBarcode.EditValue = "";
                            //txtBarcode.Focus();
                            return;
                        }
                        else
                        {
                            int dt_index = dt_gv2.Rows.IndexOf(drs[0]);
                            gv2.SelectRow(dt_index);
                            txtBarcode.EditValue = "";
                            //txtBarcode.Focus();
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show("此条码无效!", "提示", MessageBoxButtons.OK);
                        txtBarcode.EditValue = "";
                        //txtBarcode.Focus();
                        return;
                    }
                }
            }
            else if (e.KeyCode == Keys.Delete)
            {
                txtBarcode.EditValue = "";
                //txtBarcode.Focus();
            }
        }

        private void gc1_MouseClick(object sender, MouseEventArgs e)
        {
            DevExpress.XtraGrid.Views.Grid.ViewInfo.GridHitInfo hi = gv1.CalcHitInfo(new Point(e.X, e.Y));
            if (!hi.InRow)
            {
                allow_confirm = false;
            }
            else
            {
                allow_confirm = true;
            }
        }

        private void bBprint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Wf_Print_New();
        }

        private void gc1_ProcessGridKey(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F2)
            {
                bbtnSave.PerformClick();
            }
        }

        private void barLargeButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (masterdr == null) return;
            if (gv2.RowCount < 0 || txtPatientID.Text.Length < 1) return;
            //FrmAllergyDrugsQry fadq = new FrmAllergyDrugsQry(masterdr["PATIENT_ID"].ToString(), "1");
            //fadq.ShowDialog();

        }

        /// <summary>
        /// 全景病历按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barLargeButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (masterdr == null) return;
            if (gv2.RowCount < 0 || txtPatientID.Text.Length < 1) return;
            string patientid = masterdr["PATIENT_ID"].ToString();
            //string strUrl = "http://192.168.0.74:8070/TJEMRProject/medicalRecordMenu?authorityId=46354724-8&patId=" + patientid + @"&user=" + PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
            //string panoramaUrl = PlatCommon.SysBase.SystemParm.GetParaValue("PANORAMA_URL", "*", "*", "*", "");
            string panoramaUrl = "";
            PublicFunction.GetInterfaceConfigDict("_PANORAMA_URL", ref panoramaUrl);
            string strUrl = panoramaUrl + "?authorityId=" + PlatCommon.SysBase.SystemParm.HisUnitCode + "&patId=" + patientid;
            System.Diagnostics.Process.Start(strUrl);
        }

        /// <summary>
        /// 右键菜单事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void contextMenuStrip1_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            if (gv2.RowCount > 0)
            {
                int[] rhs = gv2.GetSelectedRows();
                int len = rhs.Count<int>();
                DataRow drow = this.gv2.GetDataRow(this.gv2.FocusedRowHandle);

                string DRUG_CODE = drow["DRUG_CODE"].ToString();    //药品ID
                string DRUG_NAME = drow["DRUG_NAME"].ToString();    //药品名称
                try
                {
                    if (canshu.Equals("1") && Load_State == 1)
                    {
                        if ((e.ClickedItem).Name == "toolStripMenuItem1")   //药品说明书
                        {
                            PlatPublic.Common.MeiKang_Transfer.MDC_DoSetDrug(DRUG_CODE, DRUG_NAME);
                            contextMenuStrip1.Visible = false;
                            PlatPublic.Common.MeiKang_Transfer.MDC_DoRefDrug(11);
                            contextMenuStrip1.Visible = true;
                        }
                        else if ((e.ClickedItem).Name == "toolStripMenuItem2") //简要信息
                        {
                            PlatPublic.Common.MeiKang_Transfer.MDC_DoSetDrug(DRUG_CODE, DRUG_NAME);
                            contextMenuStrip1.Visible = false;
                            PlatPublic.Common.MeiKang_Transfer.MDC_DoRefDrug(51);
                            contextMenuStrip1.Visible = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void gv1_RowCountChanged(object sender, EventArgs e)
        {

        }

        private void FrmOutpOnePresdispDeliver_FormClosed(object sender, FormClosedEventArgs e)
        {
            //关闭美康合理用药
            if (canshu.Equals("1") && Load_State == 1)
            {
                PlatPublic.Common.MeiKang_Transfer.MDC_Quit();
            }

            // 停止语音提示
            StopVoiceAlert();
        }

        private void gv1_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            GridView view = sender as GridView;
            int hand = e.RowHandle;//行号
            if (hand >= 0)
            {
                string needAlert = view.GetRowCellValue(hand, view.Columns["PRESC_ATTR"]).ToString();
                if (needAlert == "毒麻处方")
                {
                    e.Appearance.BackColor = Color.Red;
                }
                e.Appearance.ForeColor = Color.Blue;

            }
        }

        private void gv1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13 || e.KeyChar == 17)//回车或Ctrl
            {
                BbtnSave_ItemClick(bbtnSave, null);
                if (gv1.RowCount > 0)
                {
                    gv1.Focus();
                    gv1.FocusedRowHandle = 0;
                }

            }
        }

        #region 读卡模块
        Dictionary<string, string> dic = new Dictionary<string, string>();
        private bool useHealthyCard = false;  //是否使用电子健康卡
        /// <summary>
        /// 设置卡按钮状态
        /// </summary>
        private void SetCardButton()
        {
            //身份证
            if (dic.ContainsKey("READ_ID_NO_TYPE") && dic["READ_ID_NO_TYPE"] != "00")
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //院内卡
            if (dic.ContainsKey("READ_CARD_NO_TYPE") && dic["READ_CARD_NO_TYPE"] != "00")
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //电子健康卡
            if (dic.ContainsKey("READ_HEALTH_NO_TYPE") && dic["READ_HEALTH_NO_TYPE"] != "00")
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }

        }
        /// <summary>
        /// 开放刷卡按钮
        /// </summary>
        private void OpenButton()
        {
            //barInsuranceCard.Enabled = true;
            barIDCard.Enabled = true;
            barHospitalCard.Enabled = true;
            barHealthCard.Enabled = true;
        }
        /// <summary>
        /// 禁用刷卡按钮
        /// </summary>
        private void CloseButton()
        {
            //barInsuranceCard.Enabled = false;
            barIDCard.Enabled = false;
            barHospitalCard.Enabled = false;
            barHealthCard.Enabled = false;
        }
        /// <summary>
        /// 读医保卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>


        /// <summary>
        /// 读身份证
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barIDCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                bbtnrefresh_ItemClick(null, null);
                CloseButton();
                IdentityCardMessage idcm = new IdentityCardMessage();
                if (ReadIdentityCardBusiness.ReadIdentityCard(dic["READ_ID_NO_TYPE"], ref idcm) != 1)
                {
                    OpenButton();
                    return;
                }
                if (string.IsNullOrEmpty(idcm.IdCardno))
                {
                    OpenButton();
                    return;
                }

                OpenButton();

                string ls_patient_id;
                string ls_pName, ls_pSex, ls_pNation, ls_pBirth, ls_pAddress, ls_pCertNo;

                ls_pName = idcm.Name;
                ls_pSex = idcm.Sex;
                ls_pNation = idcm.Nation;
                ls_pBirth = idcm.Born;
                ls_pAddress = idcm.Address;
                ls_pCertNo = idcm.IdCardno;
                string sql = "SELECT PATIENT_ID FROM PAT_MASTER_INDEX WHERE ID_NO='" + ls_pCertNo + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
                ls_patient_id = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
                if (string.IsNullOrEmpty(ls_patient_id))
                {
                    XtraMessageBox.Show("没有找到身份证信息！", "提示", MessageBoxButtons.OK);
                    return;
                }
                cmbType.EditValue = "病人ID";
                txtInput.EditValue = ls_patient_id;
                KeyEventArgs kea = new KeyEventArgs(Keys.Enter);
                txtInput_KeyDown(null, kea);

            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        /// <summary>
        /// 读院内卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barHospitalCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                bbtnrefresh_ItemClick(null, null);
                CloseButton();
                string idCardNO = "";
                if (ReadHospitalCardBusiness.ReadHospitalCard(dic["READ_CARD_NO_TYPE"], ref idCardNO) != 1)
                {
                    OpenButton();
                    return;
                }
                if (string.IsNullOrEmpty(idCardNO))
                {
                    OpenButton();
                    return;
                }
                OpenButton();
                string ls_cardno = idCardNO;
                Comm.ucc2_his33_card_manager luo_card = new Comm.ucc2_his33_card_manager();
                string ls_patient_id = "";
                if (luo_card.uf_check_valid(ref ls_patient_id, ls_cardno, false) != 0) return;
                cmbType.EditValue = "病人ID";
                txtInput.EditValue = ls_patient_id;
                KeyEventArgs kea = new KeyEventArgs(Keys.Enter);
                txtInput_KeyDown(null, kea);
                ///


            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        /// <summary>
        /// 读健康卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        #endregion
        private void FrmOutpOnePresdispDeliver_Shown(object sender, EventArgs e)
        {
            dic = ReadIdentityCardBusiness.GetCardParameter(this.AppCode);
            SetCardButton();
        }

        private void barHealthCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //通用方法未提供
            NM_Service.NMService.ServerPublicClient spc = new NM_Service.NMService.ServerPublicClient();
            DataTable dt = new DataTable();
            dt.Columns.Add("OUT_VERIFY_NO");//外部验证流水号
            dt.Columns.Add("OUT_VERIFY_TIME");//
            dt.Columns.Add("OPERATOR_ID");//用卡验证操作员编号
            dt.Columns.Add("OPERATOR_NAME");//用卡验证操作员姓名
            dt.Columns.Add("CERTIFICATE_MODE");//认证方式
            dt.Columns.Add("IP_ADDR");//ip 地址 
            dt.Columns.Add("TREATMENT_CODE");//诊疗环节 
            dt.Columns.Add("office_code");//科室
            string datetimenow = DateTime.Now.ToString("yyyyMMddHHmmss");
            //string lst = SystemParm.GetParaValue("DZJKM_URL","*", "*", "*", "");
            DataRow dr2 = dt.NewRow();
            dr2["OUT_VERIFY_NO"] = datetimenow;
            dr2["OUT_VERIFY_TIME"] = datetimenow;
            dr2["OPERATOR_ID"] = SystemParm.LoginUser.ID;
            dr2["OPERATOR_NAME"] = SystemParm.LoginUser.NAME;
            dr2["CERTIFICATE_MODE"] = "";
            dr2["IP_ADDR"] = "";
            dr2["TREATMENT_CODE"] = "当日挂号";
            dr2["office_code"] = "挂号处";
            dt.Rows.Add(dr2);
            string term_id = "35020010001";
            string out_jsons = "";

            DataTable out_dt = new DataTable();
            LstVirtualCardChk lvcc = new LstVirtualCardChk();

            string out_jsons1 = lvcc.YzDzjkkEwm(spc, dt, term_id, ref out_jsons, ref out_dt);
            string indexid = string.Empty;
            if (out_dt.Columns.Contains("ID_NO"))
            {
                string strID_NO = out_dt.Rows[0]["ID_NO"].ToString();
                //
                string ls_patient_id;

                string sql = "SELECT PATIENT_ID FROM PAT_MASTER_INDEX WHERE ID_NO='" + strID_NO + "' and HIS_UNIT_CODE='" + SystemParm.HisUnitCode + "' ";
                ls_patient_id = new NM_Service.NMService.ServerPublicClient().GetSingleValue(sql);
                if (string.IsNullOrEmpty(ls_patient_id))
                {
                    XtraMessageBox.Show("没有找到身份证信息！", "提示", MessageBoxButtons.OK);
                    return;
                }
                cmbType.EditValue = "病人ID";
                txtInput.EditValue = ls_patient_id;
                KeyEventArgs kea = new KeyEventArgs(Keys.Enter);
                txtInput_KeyDown(null, kea);
                //
            }
        }

        private void gv2_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                if (e.RowHandle < 0)
                    return;

                DataRow selDataRow = gv2.GetDataRow(e.RowHandle);//当前操作诊疗项目数据行的datarow
                                                                 //string itemClass = selDataRow["ITEM_CLASS"].ToString();
                                                                 //string itemCode = selDataRow["ITEM_CODE"].ToString();
                                                                 //string itemName = selDataRow["ITEM_NAME"].ToString();
                                                                 //string hisUnitCode = SystemParm.HisUnitCode;
                if (e.Column == colCaoZuo)
                {
                }
            }
            catch { }
        }

        private void repositoryItemHyperLinkEdit1_Click(object sender, EventArgs e)
        {
            ShowDllForm();

        }
        string traceabilityCodes = "";
        private void ShowDllForm()
        {
            traceabilityCodes = "";
            // 假设DLL名为"MyDll.dll"，且窗体类型为MyDll.MyForm
            string dllName = "Tjhis.Interface.Station.dll";
            string formTypeName = "Tjhis.Interface.Station.Phstock.FrmDrugTraceabilityCodeInput";

            // 加载DLL
            Assembly dllAssembly = Assembly.LoadFrom(dllName);

            // 获取窗体类型
            Type formType = dllAssembly.GetType(formTypeName);

            // 如果窗体是非公共的，需要设置ReflectionPermission
            // ...

            // 创建并显示窗体实例
            Form myForm = (Form)Activator.CreateInstance(formType);
            if (myForm.ShowDialog() == DialogResult.OK) // 或者 myForm.Show()，如果不想以模态窗口的方式显示
            {
                traceabilityCodes = ((Tjhis.Interface.Station.Phstock.FrmDrugTraceabilityCodeInput)myForm).traceabilityCodes;
            }
        }

        private void repositoryItemTextEdit1_Click(object sender, EventArgs e)
        {

        }

        private void gv2_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && e.Clicks == 1)
            {
                GridHitInfo hitinfo = gv2.CalcHitInfo(e.Location);
                if (hitinfo.InRowCell && hitinfo.Column == colCaoZuo)
                {
                    int rowIndex = hitinfo.RowHandle;
                    FrmDrugTraceabilityCodeInput codeInput;
                    DataRow selDataRow = gv2.GetDataRow(rowIndex);
                    string isSplitPackage = TraceCode.IsSplitPackage(selDataRow["DRUG_CODE"].ToString(), selDataRow["DRUG_SPEC"].ToString(),selDataRow["FIRM_ID"].ToString());
                    bool isCl = (isSplitPackage.Equals("1"));
                    if (selDataRow["CZ"] != DBNull.Value)
                    {
                        codeInput = new FrmDrugTraceabilityCodeInput(selDataRow["CZ"].ToString(), int.Parse(selDataRow["QUANTITY"].ToString()), "门诊发药", isCl);
                    }
                    else
                    {
                        codeInput = new FrmDrugTraceabilityCodeInput(int.Parse(selDataRow["QUANTITY"].ToString()), "门诊发药", isCl);
                    }
                    if (codeInput.ShowDialog() == DialogResult.OK)
                    {
                        traceabilityCodes = codeInput.traceabilityCodes;
                        selDataRow["CZ"] = traceabilityCodes;
                    }

                }
            }
        }

        private void txtPrescDate_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void FrmOutpOnePresdispDeliver_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        private void gv1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gv1.FocusedRowHandle < 0 || gv1.DataRowCount == 0)
            {
                clearPresc();
                return;
            }
            doPass = false;
            SetBaseInfoAndDetailByPrescDateAndPrescNo(gv1.FocusedRowHandle);
        }



        #region Wf_Print_New
        private void Wf_Print_New()
        {
            try
            {
                DataRow prescdr = prescdr_p;
                if (prescdr == null) return;
                DataTable detadt = detadt_p;
                DataRow[] drs = detadt.Select("PRESC_NO='" + prescdr["PRESC_NO"] + "' and PRESC_DATE='" + prescdr["PRESC_DATE"] + "'");
                Hashtable hasParam = new Hashtable
                {
                    { "PRESC_DATE",  prescdr["PRESC_DATE"]  } , { "PRESC_NO",  prescdr["PRESC_NO"]  }
                };

                //报表参数设置
                List<Parameter> paras = new List<Parameter>
                    {
                        new Parameter{ Name = "DRUG_WINDOW", Value = DeptName }

                    };

                if (drs.Length > 0)
                {
                    if (txtPrescAttr.Text.Equals("麻醉、精一") || txtPrescAttr.Text.Equals("麻、精一"))
                    {
                        DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊处方发药_精一精二", hasParam, this.AppCode);
                        XtraReportHelper.Print("门诊处方发药_精一精二", dsPrint, this.AppCode);
                    }
                    else
                    {
                        if (prescdr["PRESC_TYPE"].ToString().Equals("0")) //西药处方
                        {
                            DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊处方发药_普通西药", hasParam, this.AppCode);
                            XtraReportHelper.Print("门诊处方发药_普通西药", dsPrint, paras, this.AppCode);
                        }
                        else  //中药处方
                        {
                            DataSet dsPrint = XtraReportHelper.GetPrintData_DataBase("门诊处方发药_普通中药", hasParam, this.AppCode);
                            XtraReportHelper.Print("门诊处方发药_普通中药", dsPrint, this.AppCode);

                        }
                    }
                }

            }
            catch (Exception ex)
            {
                throw new Exception("\r\nFrmOutpOnePresdispDeliver-Wf_Print_New-Exception: " + ex.Message);
            }
        }

        #endregion

        #region 语音提示功能

        /// <summary>
        /// 初始化语音提示功能
        /// </summary>
        private void InitVoiceAlert()
        {
            try
            {
                // 获取语音提示开关参数
                string voicePrompt = PlatCommon.SysBase.SystemParm.GetParameterValue("VOICE_PROMPT", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                voiceEnabled = voicePrompt == "1";

                if (voiceEnabled)
                {
                    // 初始化定时器，每30秒检查一次新处方
                    voiceTimer = new System.Windows.Forms.Timer();
                    voiceTimer.Interval = 30000; // 30秒
                    voiceTimer.Tick += VoiceTimer_Tick;
                    voiceTimer.Start();

                    // 记录初始处方数量
                    if (prescdt != null)
                    {
                        lastPrescCount = prescdt.Rows.Count;
                    }
                }
            }
            catch (Exception ex)
            {
                // 语音初始化失败，静默处理
            }
        }

        /// <summary>
        /// 定时器事件，定期检查新处方
        /// </summary>
        private void VoiceTimer_Tick(object sender, EventArgs e)
        {
            if (!voiceEnabled) return;

            try
            {
                // 获取当前处方数据
                string chargeflag = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGE_FLAG", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                string presc_verified = PlatCommon.SysBase.SystemParm.GetParameterValue("PRESC_VERIFIED", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                string chargetypes = PlatCommon.SysBase.SystemParm.GetParameterValue("CHARGETYPES", this.AppCode, this.DeptCode, SystemParm.LoginUser.EMP_NO, PlatCommon.SysBase.SystemParm.HisUnitCode);
                DateTime starttime = DateTime.Now.AddDays(-showPrescDays);
                DateTime endtime = DateTime.Now.AddDays(1);

                DataTable currentPrescdt = null;
                if (chargeflag == "1")
                {
                    if (presc_verified == "1")
                    {
                        currentPrescdt = GetDrugPrescMasterTempByChargeFlag1Verified1(starttime, endtime, this.DeptCode, chargetypes);
                    }
                    else
                    {
                        currentPrescdt = GetDrugPrescMasterTempByChargeFlag1Verified0(starttime, endtime, this.DeptCode, chargetypes);
                    }
                }
                else
                {
                    if (presc_verified == "1")
                    {
                        currentPrescdt = GetDrugPrescMasterTempByChargeFlag0Verified1(starttime, endtime, this.DeptCode);
                    }
                    else
                    {
                        currentPrescdt = GetDrugPrescMasterTempByChargeFlag0Verified0(starttime, endtime, this.DeptCode);
                    }
                }

                // 检测新处方并播放语音提示
                if (currentPrescdt != null)
                {
                    CheckNewPrescAndPlayVoice(currentPrescdt);
                }
            }
            catch (Exception ex)
            {
                // 定时检查失败，静默处理
            }
        }

        /// <summary>
        /// 检测新处方并播放语音提示
        /// </summary>
        /// <param name="currentPrescdt">当前处方数据</param>
        private void CheckNewPrescAndPlayVoice(DataTable currentPrescdt)
        {
            if (!voiceEnabled || currentPrescdt == null) return;

            try
            {
                int currentCount = currentPrescdt.Rows.Count;

                // 检查是否有待处理的处方
                if (currentCount > 0)
                {
                    // 只要有待处理的处方，就播放语音提示（包括新处方和已有处方的定期提醒）
                    Tjhis.Presdisp.Station.Globals.VoiceHelper.PlayNewPrescAlert();
                }

                // 更新处方数量
                lastPrescCount = currentCount;
            }
            catch (Exception ex)
            {
                // 语音提示失败，静默处理
            }
        }

        /// <summary>
        /// 停止语音提示
        /// </summary>
        private void StopVoiceAlert()
        {
            try
            {
                if (voiceTimer != null)
                {
                    voiceTimer.Stop();
                    voiceTimer.Dispose();
                    voiceTimer = null;
                }

                Tjhis.Presdisp.Station.Globals.VoiceHelper.StopVoice();
            }
            catch (Exception ex)
            {
                // 停止语音失败，静默处理
            }
        }

        /// <summary>
        /// 记录发药成功的详细日志
        /// </summary>
        /// <param name="masterdr">处方主记录</param>
        private void WriteDispenseSuccessLog(DataRow masterdr)
        {
            try
            {
                string logPath = @"..\Client\LOG\exLOG\";
                if (!System.IO.Directory.Exists(logPath))
                {
                    System.IO.Directory.CreateDirectory(logPath);
                }

                string fileName = $"处方发药成功_{DateTime.Now:yyyyMMdd}.log";
                string fullPath = System.IO.Path.Combine(logPath, fileName);

                StringBuilder logEntry = new StringBuilder();
                logEntry.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ==================== 发药成功 ====================");
                logEntry.AppendLine($"处方日期：{masterdr["PRESC_DATE"]}");
                logEntry.AppendLine($"处方号：{masterdr["PRESC_NO"]}");
                logEntry.AppendLine($"患者ID：{masterdr["PATIENT_ID"]}");
                logEntry.AppendLine($"患者姓名：{masterdr["NAME"]}");
                logEntry.AppendLine($"药房：{masterdr["DISPENSARY"]}");
                logEntry.AppendLine($"发药人：{SystemParm.LoginUser.NAME}");
                logEntry.AppendLine($"发药时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 记录发药明细
                DataTable detailTable = (DataTable)gc2.DataSource;
                if (detailTable != null && detailTable.Rows.Count > 0)
                {
                    logEntry.AppendLine("发药明细：");
                    foreach (DataRow detailRow in detailTable.Rows)
                    {
                        logEntry.AppendLine($"  - 药品：{detailRow["DRUG_NAME"]} ({detailRow["DRUG_CODE"]})");
                        logEntry.AppendLine($"    数量：{detailRow["QUANTITY"]} {detailRow["PACKAGE_UNITS"]}");
                        logEntry.AppendLine($"    批次：{detailRow["BATCH_CODE"]}");
                        logEntry.AppendLine($"    厂家：{detailRow["FIRM_ID"]}");
                    }
                }

                logEntry.AppendLine("========================================================");
                logEntry.AppendLine();

                System.IO.File.AppendAllText(fullPath, logEntry.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 日志写入失败不影响主流程，但记录到统一库存验证日志中
                try
                {
                    string logPath = @"..\Client\LOG\exLOG\";
                    string fileName = $"处方发药站_库存验证_{DateTime.Now:yyyyMMdd}.log";
                    string fullPath = System.IO.Path.Combine(logPath, fileName);
                    string errorLog = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [发药成功日志] 写入发药成功日志失败：{ex.Message}{Environment.NewLine}";
                    System.IO.File.AppendAllText(fullPath, errorLog, Encoding.UTF8);
                }
                catch
                {
                    // 完全忽略日志写入异常
                }
            }
        }

        #endregion

    }
}
